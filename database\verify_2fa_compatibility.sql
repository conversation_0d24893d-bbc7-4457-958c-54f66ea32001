-- Verification Script for 2FA Database Compatibility
-- Run this BEFORE deploying 2FA tables to check compatibility

-- Check users table structure
DESCRIBE users;

-- Check if users table uses InnoDB (required for foreign keys)
SELECT TABLE_NAME, ENGINE, TABLE_COLLATION 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users';

-- Check existing admin users
SELECT id, name, email, role, created_at 
FROM users 
WHERE role = 'ADMIN' 
ORDER BY created_at;

-- Check for any existing 2FA tables
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 'admin_2fa%';

-- Check for any existing foreign key constraints that might conflict
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
AND REFERENCED_TABLE_NAME = 'users'
ORDER BY TABLE_NAME;

-- Test foreign key compatibility (this should work if everything is compatible)
-- This creates a temporary table to test the foreign key constraint
CREATE TEMPORARY TABLE test_fk_compatibility (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    test_field VARCHAR(50),
    CONSTRAINT test_fk FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- If the above succeeds, drop the test table
DROP TEMPORARY TABLE test_fk_compatibility;

-- Show success message
SELECT 'SUCCESS: Foreign key compatibility verified!' as status;
