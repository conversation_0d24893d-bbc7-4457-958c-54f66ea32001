<?php
/**
 * Blog Page - Professional Beauty Blog
 * Displays all blog posts or a single blog post with modern design
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Check if we're viewing a single blog post
$slug = $_GET['slug'] ?? null;
$search = $_GET['search'] ?? '';
$singlePost = null;

if ($slug) {
    // Get the single blog post by slug
    $singlePost = $database->fetch(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.slug = ? AND bp.status = 'published'",
        [$slug]
    );

    // If post not found, redirect to blog index
    if (!$singlePost) {
        redirect('/blog');
    }

    $pageTitle = $singlePost['title'] . ' - Flix Salonce Blog';
} else {
    // Get search and pagination parameters
    $page = (int)($_GET['page'] ?? 1);
    $limit = 9; // Increased for better grid layout
    $offset = ($page - 1) * $limit;

    // Build search query
    $searchCondition = '';
    $searchConditionCount = '';
    $searchParams = [];
    if (!empty($search)) {
        $searchCondition = " AND (bp.title LIKE ? OR bp.summary LIKE ? OR bp.full_content LIKE ?)";
        $searchConditionCount = " AND (title LIKE ? OR summary LIKE ? OR full_content LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $searchParams = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Get total posts count
    $totalPosts = $database->fetch(
        "SELECT COUNT(*) as count FROM blog_posts WHERE status = 'published'" . $searchConditionCount,
        $searchParams
    )['count'];

    // Get featured posts (latest 3 posts)
    $featuredPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT 3"
    );

    // Get regular blog posts (excluding featured ones if on first page)
    $excludeFeatured = ($page === 1 && empty($search)) ? " AND bp.id NOT IN ('" . implode("','", array_column($featuredPosts, 'id')) . "')" : '';

    $blogPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'" . $searchCondition . $excludeFeatured . "
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT $limit OFFSET $offset",
        $searchParams
    );

    $totalPages = ceil($totalPosts / $limit);
    $pageTitle = !empty($search) ? "Search: $search - Blog" : 'Beauty Blog - Flix Salonce';
}

// Include header
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Blog Styling */
FeaturedFeatured:root {
    --salon-gold: #f59e0b;
    --salon-gold-light: #fbbf24;
    --salon-gold-dark: #d97706;
    --salon-black: #0f172a;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;
}

/* Card Styling */
.blog-card {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(245, 158, 11, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.blog-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.blog-card:hover::after {
    opacity: 1;
}

.blog-card:hover {
    transform: translateY(-8px);
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(245, 158, 11, 0.1);
}

.featured-card {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(30, 41, 59, 0.9) 100%);
    border: 1px solid rgba(245, 158, 11, 0.2);
    position: relative;
}

.featured-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(245, 158, 11, 0.15), transparent 70%);
    pointer-events: none;
}

.featured-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 35px 60px -12px rgba(245, 158, 11, 0.2);
}

.search-glow {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.text-gradient {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Animations */
.animate-float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pulse-gold {
    0%, 100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4); }
    50% { box-shadow: 0 0 0 10px rgba(245, 158, 11, 0); }
}

.animate-pulse-gold {
    animation: pulse-gold 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Text Utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
}

::-webkit-scrollbar-thumb {
    background: rgba(245, 158, 11, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(245, 158, 11, 0.7);
}

/* Improved Focus States */
a:focus, button:focus, input:focus {
    outline: 2px solid rgba(245, 158, 11, 0.5);
    outline-offset: 2px;
}

/* Improved Transitions */
a, button, input {
    transition: all 0.3s ease;
}

/* Improved Typography */
.prose h2, .prose h3, .prose h4 {
    letter-spacing: -0.025em;
}

.prose p {
    letter-spacing: 0.015em;
}
</style>

<!-- Hero Section -->
<section class="relative min-h-[70vh] bg-gradient-to-br from-salon-black via-secondary-900 to-salon-black overflow-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <!-- Animated Gradient Orbs -->
        <div class="absolute top-20 left-10 w-72 h-72 bg-salon-gold/5 rounded-full blur-3xl animate-float opacity-70"></div>
        <div class="absolute bottom-20 right-10 w-96 h-96 bg-salon-gold/3 rounded-full blur-3xl animate-float opacity-80" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-salon-gold/5 to-transparent rounded-full blur-3xl"></div>
        
        <!-- Subtle Moving Lines -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: linear-gradient(to right, rgba(245,158,11,0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(245,158,11,0.1) 1px, transparent 1px); background-size: 60px 60px;"></div>
        </div>
        
        <!-- Radial Dot Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(245,158,11,0.3) 1px, transparent 0); background-size: 40px 40px;"></div>
        </div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 md:py-24 flex items-center min-h-[70vh]">
        <div class="w-full text-center">
            <?php if ($singlePost): ?>
                <!-- Single Post Hero - Improved Layout -->
                <div class="max-w-4xl mx-auto">
                    <!-- Category Badge -->
                    <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6 backdrop-blur-sm border border-salon-gold/10 shadow-lg shadow-salon-gold/5 animate-pulse-gold">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a2 2 0 012-2z"></path>
                        </svg>
                        Blog Article
                    </div>
                    
                    <!-- Post Title with Improved Typography -->
                    <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold font-serif text-white mb-6 leading-tight tracking-tight">
                        <?= htmlspecialchars($singlePost['title']) ?>
                    </h1>
                    
                    <!-- Author & Meta Info with Improved Layout -->
                    <div class="flex flex-wrap items-center justify-center gap-4 sm:gap-6 text-gray-300 mb-8">
                        <div class="flex items-center bg-secondary-800/30 backdrop-blur-sm px-4 py-2 rounded-full">
                            <svg class="w-5 h-5 mr-2 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <?= htmlspecialchars($singlePost['author_name'] ?? 'Flix Salonce') ?>
                        </div>
                        <div class="flex items-center bg-secondary-800/30 backdrop-blur-sm px-4 py-2 rounded-full">
                            <svg class="w-5 h-5 mr-2 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <?= $singlePost['publish_date'] ? date('F j, Y', strtotime($singlePost['publish_date'])) : date('F j, Y', strtotime($singlePost['created_at'])) ?>
                        </div>
                        <div class="flex items-center bg-secondary-800/30 backdrop-blur-sm px-4 py-2 rounded-full">
                            <svg class="w-5 h-5 mr-2 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <?php
                            $wordCount = str_word_count(strip_tags($singlePost['full_content']));
                            $readTime = max(1, ceil($wordCount / 200));
                            echo "$readTime min read";
                            ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Blog Index Hero - Enhanced Design -->
                <div class="max-w-5xl mx-auto">
                    <!-- Category Badge -->
                    <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6 backdrop-blur-sm border border-salon-gold/10 shadow-lg shadow-salon-gold/5 animate-pulse-gold">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                        Beauty & Wellness Blog
                    </div>
                    
                    <!-- Main Heading with Enhanced Typography -->
                    <h1 class="text-5xl sm:text-6xl md:text-7xl font-bold font-serif text-white mb-6 tracking-tight">
                        Beauty <span class="text-gradient relative inline-block">
                            Insights
                            <span class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-salon-gold to-transparent rounded-full"></span>
                        </span>
                    </h1>
                    
                    <!-- Subheading with Improved Typography -->
                    <p class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-10">
                        Discover the latest trends, tips, and secrets from the world of beauty and wellness. Your journey to radiance starts here.
                    </p>

                    <!-- Enhanced Search Bar -->
                    <div class="max-w-2xl mx-auto relative z-10">
                        <form method="GET" action="<?= getBasePath() ?>/blog" class="relative group">
                            <div class="relative overflow-hidden rounded-2xl transition-all duration-300 group-hover:shadow-lg group-hover:shadow-salon-gold/10">
                                <input
                                    type="text"
                                    name="search"
                                    value="<?= htmlspecialchars($search) ?>"
                                    placeholder="Search beauty tips, tutorials, trends..."
                                    class="w-full px-6 py-4 pl-14 bg-secondary-800/70 backdrop-blur-md border border-secondary-600 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold focus:ring-2 focus:ring-salon-gold/20 transition-all duration-300"
                                >
                                <!-- Animated Search Icon -->
                                <svg class="absolute left-5 top-1/2 transform -translate-y-1/2 w-5 h-5 text-salon-gold transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <!-- Enhanced Search Button -->
                                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-salon-gold hover:bg-salon-gold-light text-black px-6 py-2 rounded-xl font-semibold transition-all duration-300 hover:shadow-md hover:shadow-salon-gold/20 hover:scale-105">
                                    Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Enhanced Breadcrumb -->
            <div class="flex items-center justify-center space-x-2 text-gray-400 mt-10 bg-secondary-800/30 backdrop-blur-sm py-2 px-4 rounded-full inline-flex mx-auto">
                <a href="<?= getBasePath() ?>/" class="hover:text-salon-gold transition-colors flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Home
                </a>
                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <?php if ($singlePost): ?>
                    <a href="<?= getBasePath() ?>/blog" class="hover:text-salon-gold transition-colors">Blog</a>
                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-salon-gold truncate max-w-[150px] sm:max-w-xs"><?= htmlspecialchars($singlePost['title']) ?></span>
                <?php else: ?>
                    <span class="text-salon-gold">Blog</span>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<div class="bg-salon-black py-20">
    <div class="max-w-7xl mx-auto px-6">
        <?php if ($singlePost): ?>
            <!-- Single Blog Post View - Enhanced Design -->
            <article class="max-w-4xl mx-auto">
                <!-- Featured Image with Improved Visual Effects -->
                <?php if ($singlePost['image_url']): ?>
                <div class="relative mb-12 rounded-2xl overflow-hidden shadow-2xl shadow-salon-gold/10 group">
                    <div class="aspect-w-16 aspect-h-9 overflow-hidden">
                        <img
                            src="<?= htmlspecialchars(getBlogImageUrl($singlePost['image_url'])) ?>"
                            alt="<?= htmlspecialchars($singlePost['title']) ?>"
                            class="w-full h-[400px] object-cover transition-transform duration-700 group-hover:scale-105"
                        >
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-salon-black/70 via-salon-black/30 to-transparent opacity-80"></div>
                    <!-- Decorative Element -->
                    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-salon-gold via-salon-gold/50 to-transparent"></div>
                </div>
                <?php endif; ?>

                <!-- Article Content with Enhanced Design -->
                <div class="bg-secondary-800/30 backdrop-blur-sm rounded-2xl border border-secondary-700/50 overflow-hidden shadow-xl shadow-salon-gold/5">
                    <div class="p-6 sm:p-8 md:p-12">
                        <!-- Article Summary with Improved Visual Design -->
                        <?php if (!empty($singlePost['summary'])): ?>
                        <div class="bg-salon-gold/10 border-l-4 border-salon-gold p-6 rounded-r-lg mb-8 relative overflow-hidden">
                            <!-- Decorative Quote Icon -->
                            <div class="absolute right-4 top-4 opacity-10">
                                <svg class="w-16 h-16 text-salon-gold" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                                </svg>
                            </div>
                            <p class="text-lg text-gray-200 italic leading-relaxed relative z-10">
                                <?= htmlspecialchars($singlePost['summary']) ?>
                            </p>
                        </div>
                        <?php endif; ?>

                        <!-- Article Body with Enhanced Typography and Styling -->
                        <div class="prose prose-lg prose-invert max-w-none">
                            <style>
                                /* Enhanced Typography and Visual Styling */
                                .prose {
                                    color: #e5e7eb;
                                    line-height: 1.8;
                                    font-size: 1.125rem;
                                    letter-spacing: 0.01em;
                                }
                                .prose h1, .prose h2, .prose h3, .prose h4 {
                                    color: #f9fafb;
                                    font-weight: 700;
                                    margin-top: 2em;
                                    margin-bottom: 1em;
                                    letter-spacing: -0.01em;
                                    position: relative;
                                }
                                .prose h1 { 
                                    font-size: 2.25em; 
                                    line-height: 1.2;
                                }
                                .prose h2 {
                                    font-size: 1.875em;
                                    line-height: 1.3;
                                    border-bottom: 2px solid rgba(245, 158, 11, 0.2);
                                    padding-bottom: 0.5em;
                                }
                                .prose h2::before {
                                    content: '';
                                    position: absolute;
                                    left: -1rem;
                                    top: 0.5em;
                                    width: 0.5rem;
                                    height: 0.5rem;
                                    background-color: #f59e0b;
                                    border-radius: 50%;
                                }
                                .prose h3 { 
                                    font-size: 1.5em; 
                                    line-height: 1.4;
                                }
                                .prose h4 { 
                                    font-size: 1.25em; 
                                    line-height: 1.5;
                                    color: #f59e0b;
                                }
                                .prose p {
                                    margin-bottom: 1.5em;
                                    font-size: 1.125em;
                                    line-height: 1.8;
                                }
                                .prose a {
                                    color: #f59e0b;
                                    text-decoration: none;
                                    border-bottom: 1px solid transparent;
                                    transition: all 0.3s ease;
                                    position: relative;
                                    padding: 0 0.1em;
                                }
                                .prose a:hover {
                                    border-bottom-color: #f59e0b;
                                    background-color: rgba(245, 158, 11, 0.1);
                                    border-radius: 0.125rem;
                                }
                                .prose ul, .prose ol {
                                    margin: 1.5em 0;
                                    padding-left: 2em;
                                }
                                .prose ul {
                                    list-style-type: none;
                                }
                                .prose ul li {
                                    position: relative;
                                    padding-left: 1.5em;
                                }
                                .prose ul li::before {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    top: 0.75em;
                                    width: 0.5em;
                                    height: 0.5em;
                                    background-color: rgba(245, 158, 11, 0.5);
                                    border-radius: 50%;
                                }
                                .prose ol {
                                    counter-reset: list-counter;
                                    list-style-type: none;
                                }
                                .prose ol li {
                                    counter-increment: list-counter;
                                    position: relative;
                                    padding-left: 1.5em;
                                }
                                .prose ol li::before {
                                    content: counter(list-counter) ".";
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    color: #f59e0b;
                                    font-weight: 600;
                                }
                                .prose li {
                                    margin-bottom: 0.75em;
                                }
                                .prose img {
                                    border-radius: 1rem;
                                    margin: 2.5em auto;
                                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
                                    display: block;
                                    max-width: 100%;
                                    height: auto;
                                    transition: transform 0.3s ease, box-shadow 0.3s ease;
                                }
                                .prose img:hover {
                                    transform: scale(1.02);
                                    box-shadow: 0 25px 30px -5px rgba(0, 0, 0, 0.4), 0 0 10px rgba(245, 158, 11, 0.2);
                                }
                                .prose blockquote {
                                    border-left: 4px solid #f59e0b;
                                    padding-left: 1.5em;
                                    margin: 2em 0;
                                    font-style: italic;
                                    color: #d1d5db;
                                    background: rgba(245, 158, 11, 0.05);
                                    padding: 1.5em 2em;
                                    border-radius: 0.5rem;
                                    position: relative;
                                }
                                .prose blockquote::before {
                                    content: '"';
                                    position: absolute;
                                    top: 0;
                                    left: 1rem;
                                    font-size: 3em;
                                    color: rgba(245, 158, 11, 0.2);
                                    font-family: serif;
                                    line-height: 1;
                                }
                                .prose code {
                                    background: rgba(245, 158, 11, 0.1);
                                    color: #f59e0b;
                                    padding: 0.25em 0.5em;
                                    border-radius: 0.25rem;
                                    font-size: 0.9em;
                                    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
                                }
                                .prose pre {
                                    background: rgba(15, 23, 42, 0.8);
                                    border: 1px solid rgba(245, 158, 11, 0.2);
                                    border-radius: 0.75rem;
                                    padding: 1.5em;
                                    overflow-x: auto;
                                    margin: 2em 0;
                                }
                                .prose pre code {
                                    background: transparent;
                                    padding: 0;
                                    border-radius: 0;
                                    color: #e5e7eb;
                                    font-size: 0.9em;
                                    line-height: 1.7;
                                }
                                /* Add smooth scrolling for anchor links */
                                html {
                                    scroll-behavior: smooth;
                                }
                                /* Improve focus styles for accessibility */
                                .prose a:focus {
                                    outline: 2px solid rgba(245, 158, 11, 0.5);
                                    outline-offset: 2px;
                                }
                            </style>
                            <?= $singlePost['full_content'] ?>
                        </div>

                        <!-- Enhanced Article Footer with Improved Design -->
                        <div class="mt-12 pt-8 border-t border-secondary-700/50">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
                                <!-- Enhanced Back Button with Animation -->
                                <a href="<?= getBasePath() ?>/blog" class="inline-flex items-center text-salon-gold hover:text-white transition-all duration-300 font-medium group bg-secondary-800/50 hover:bg-salon-gold/20 backdrop-blur-sm px-5 py-3 rounded-full border border-secondary-700/50 hover:border-salon-gold/30 shadow-sm hover:shadow-salon-gold/10">
                                    <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                    </svg>
                                    Back to all posts
                                </a>

                                <!-- Enhanced Share Buttons with Improved Visual Design -->
                                <div class="flex items-center space-x-4 bg-secondary-800/50 backdrop-blur-sm px-5 py-3 rounded-full border border-secondary-700/50">
                                    <span class="text-gray-300 font-medium">Share:</span>
                                    <div class="flex space-x-3">
                                        <!-- Twitter with Enhanced Hover Effects -->
                                        <a href="https://twitter.com/intent/tweet?text=<?= urlencode($singlePost['title']) ?>&url=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                           target="_blank"
                                           aria-label="Share on Twitter"
                                           class="w-10 h-10 bg-secondary-700/70 hover:bg-[#1DA1F2] rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-md hover:shadow-[#1DA1F2]/20 group">
                                            <svg class="w-4 h-4 text-gray-300 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                            </svg>
                                        </a>
                                        
                                        <!-- Facebook with Enhanced Hover Effects -->
                                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                           target="_blank"
                                           aria-label="Share on Facebook"
                                           class="w-10 h-10 bg-secondary-700/70 hover:bg-[#4267B2] rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-md hover:shadow-[#4267B2]/20 group">
                                            <svg class="w-4 h-4 text-gray-300 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                            </svg>
                                        </a>
                                        
                                        <!-- LinkedIn with Enhanced Hover Effects -->
                                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                           target="_blank"
                                           aria-label="Share on LinkedIn"
                                           class="w-10 h-10 bg-secondary-700/70 hover:bg-[#0A66C2] rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-md hover:shadow-[#0A66C2]/20 group">
                                            <svg class="w-4 h-4 text-gray-300 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                            </svg>
                                        </a>
                                        
                                        <!-- Copy Link Button -->
                                        <button onclick="navigator.clipboard.writeText('<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>'); this.classList.add('bg-green-600'); setTimeout(() => this.classList.remove('bg-green-600'), 1000);"
                                           aria-label="Copy link to clipboard"
                                           class="w-10 h-10 bg-secondary-700/70 hover:bg-salon-gold rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-md hover:shadow-salon-gold/20 group">
                                            <svg class="w-4 h-4 text-gray-300 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Related Posts Suggestion (if available) -->
                            <?php if (!empty($relatedPosts)): ?>
                            <div class="mt-12 pt-8 border-t border-secondary-700/50">
                                <h3 class="text-xl font-bold text-white mb-6">You might also enjoy</h3>
                                <!-- Related posts would go here if implemented -->
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </article>
        <?php else: ?>
            <!-- Search Results Header -->
            <?php if (!empty($search)): ?>
            <div class="mb-12 text-center">
                <h2 class="text-3xl font-bold text-white mb-4">
                    Search Results for "<span class="text-salon-gold"><?= htmlspecialchars($search) ?></span>"
                </h2>
                <p class="text-gray-300">
                    <?= $totalPosts ?> <?= $totalPosts === 1 ? 'post' : 'posts' ?> found
                </p>
            </div>
            <?php endif; ?>

            <!-- Enhanced Featured Posts Section (only on first page without search) -->
            <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
            <section class="mb-20 relative">
                <!-- Decorative elements -->
                <div class="absolute -top-20 -right-20 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-70 pointer-events-none"></div>
                <div class="absolute -bottom-20 -left-20 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-70 pointer-events-none"></div>
                
                <div class="flex flex-col md:flex-row md:items-end justify-between mb-12 relative">
                    <div class="relative">
                        <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-2 relative inline-block">
                            Featured <span class="text-salon-gold">Stories</span>
                            <span class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-salon-gold to-transparent rounded-full"></span>
                        </h2>
                        <p class="text-gray-300 max-w-md">Discover our latest beauty insights and trends</p>
                    </div>
                    <a href="<?= getBasePath() ?>/blog" class="mt-4 md:mt-0 text-sm text-salon-gold hover:text-white transition-colors duration-300 flex items-center group self-start md:self-auto">
                        View all stories
                        <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Enhanced Main Featured Post -->
                    <?php $mainPost = $featuredPosts[0]; ?>
                    <div class="lg:col-span-2">
                        <article class="featured-card rounded-2xl overflow-hidden h-full group relative bg-black/80 backdrop-blur-sm border border-gray-800/50 hover:border-salon-gold/50 transition-all duration-500 shadow-xl hover:shadow-salon-gold/10">
                            <!-- Decorative elements -->
                            <div class="absolute -top-10 -right-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 pointer-events-none"></div>
                            <div class="absolute -bottom-10 -left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 delay-100 pointer-events-none"></div>
                            
                            <div class="relative h-80 lg:h-96 overflow-hidden">
                                <?php if ($mainPost['image_url']): ?>
                                    <img
                                        src="<?= htmlspecialchars(getBlogImageUrl($mainPost['image_url'])) ?>"
                                        alt="<?= htmlspecialchars($mainPost['title']) ?>"
                                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                        loading="lazy"
                                    >
                                    <!-- Enhanced gradient overlay -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-salon-black via-salon-black/50 to-transparent opacity-80 group-hover:opacity-70 transition-opacity duration-500"></div>
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-salon-black flex items-center justify-center">
                                        <svg class="w-20 h-20 text-salon-gold/60 animate-pulse-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Enhanced featured badge -->
                                <div class="absolute top-4 left-4 z-10">
                                    <span class="bg-salon-gold/90 text-black px-4 py-1.5 rounded-full text-sm font-bold shadow-lg transform group-hover:scale-105 transition-transform duration-300 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        Featured
                                    </span>
                                </div>
                            </div>
                            
                            <div class="p-8 relative z-10">
                                <!-- Enhanced metadata with icons -->
                                <div class="flex flex-wrap items-center text-gray-400 text-sm mb-4 space-x-4">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= $mainPost['publish_date'] ? date('F j, Y', strtotime($mainPost['publish_date'])) : date('F j, Y', strtotime($mainPost['created_at'])) ?>
                                    </span>
                                    <span class="text-salon-gold/50">•</span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= max(1, ceil(str_word_count(strip_tags($mainPost['full_content'])) / 200)) ?> min read
                                    </span>
                                </div>
                                
                                <!-- Enhanced title with hover effect -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>" class="block">
                                <h3 class="text-2xl font-bold text-white mb-3 line-clamp-2 group-hover:text-salon-gold transition-colors duration-300">
                                    <?= htmlspecialchars($mainPost['title']) ?>
                                </h3>
                            </a>
                                
                                <!-- Enhanced summary with better typography -->
                                <p class="text-gray-300 mb-6 line-clamp-3 leading-relaxed">
                                    <?= !empty($mainPost['summary']) ? htmlspecialchars($mainPost['summary']) : htmlspecialchars(substr(strip_tags($mainPost['full_content']), 0, 200)) . '...' ?>
                                </p>
                                
                                <!-- Enhanced call-to-action button -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>"
                                   class="inline-flex items-center text-salon-gold hover:text-white font-semibold transition-all duration-300 group-hover:translate-x-2 transition-transform ease-in-out bg-black/80 hover:bg-salon-gold/20 px-5 py-2 rounded-full">
                                    Read Full Story
                                    <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                    </div>

                    <!-- Enhanced Side Featured Posts -->
                    <div class="space-y-6">
                        <?php for ($i = 1; $i < min(3, count($featuredPosts)); $i++): ?>
                            <?php $sidePost = $featuredPosts[$i]; ?>
                            <article class="blog-card rounded-xl overflow-hidden group relative bg-black/80 backdrop-blur-sm border border-gray-800/50 hover:border-salon-gold/50 transition-all duration-500 shadow-lg hover:shadow-salon-gold/10">
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($sidePost['slug']) ?>" class="absolute inset-0 z-10" aria-label="Read more about <?= htmlspecialchars($sidePost['title']) ?>"></a>
                                <!-- Decorative element -->
                                <div class="absolute -bottom-8 -right-8 w-24 h-24 bg-salon-gold/10 rounded-full blur-2xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 pointer-events-none"></div>
                                
                                <div class="flex flex-col sm:flex-row relative">
                                    <div class="sm:w-1/3 h-32 sm:h-auto relative overflow-hidden">
                                        <?php if ($sidePost['image_url']): ?>
                                            <img
                                                src="<?= htmlspecialchars(getBlogImageUrl($sidePost['image_url'])) ?>"
                                                alt="<?= htmlspecialchars($sidePost['title']) ?>"
                                                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                                loading="lazy"
                                            >
                                            <!-- Gradient overlay -->
                                            <div class="absolute inset-0 bg-gradient-to-r from-transparent to-salon-black/70 sm:bg-gradient-to-r opacity-0 sm:opacity-70 group-hover:opacity-60 transition-opacity duration-500"></div>
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-gray-900 flex items-center justify-center">
                                                <svg class="w-8 h-8 text-salon-gold/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1 p-4">
                                        <div class="text-xs text-gray-400 mb-2">
                                            <?= $sidePost['publish_date'] ? date('M j, Y', strtotime($sidePost['publish_date'])) : date('M j, Y', strtotime($sidePost['created_at'])) ?>
                                        </div>
                                        <h4 class="text-sm font-semibold text-white line-clamp-2 group-hover:text-salon-gold transition-colors">
                                            <?= htmlspecialchars($sidePost['title']) ?>
                                        </h4>
                                        <div class="inline-flex items-center text-salon-gold text-xs font-semibold transition-all duration-300 mt-2">
                                            Read Full Story
                                            <svg class="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        <?php endfor; ?>
                    </div>
                </div>
            </section>
            <?php endif; ?>

            <!-- Enhanced Regular Blog Posts Grid -->
            <?php if (!empty($blogPosts)): ?>
            <section>
                <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
                    <div class="flex items-center justify-between mb-12 relative">
                        <!-- Decorative elements -->
                        <div class="absolute -top-10 -left-10 w-32 h-32 bg-salon-gold/5 rounded-full blur-3xl"></div>
                        
                        <div class="relative z-10">
                            <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-3 relative inline-block">
                                Latest <span class="text-salon-gold relative">Articles
                                    <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-salon-gold/30"></span>
                                </span>
                                <!-- Decorative star -->
                                <span class="absolute -top-4 -right-6 text-salon-gold/70 text-sm animate-pulse-gold">✧</span>
                            </h2>
                            <p class="text-gray-300 max-w-md">Stay updated with our beauty and wellness insights</p>
                        </div>
                        <div class="hidden md:flex items-center space-x-2">
                            <div class="w-2 h-2 rounded-full bg-salon-gold/70"></div>
                            <div class="w-32 h-px bg-gradient-to-r from-salon-gold to-transparent"></div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($blogPosts as $post): ?>
                        <article class="blog-card bg-black/80 backdrop-blur-sm rounded-xl overflow-hidden group h-full flex flex-col relative border border-gray-800/80 hover:border-salon-gold/80 transition-all duration-500 shadow-lg hover:shadow-salon-gold/10">
                            <!-- Decorative elements -->
                            <div class="absolute -bottom-10 -right-10 w-32 h-32 bg-salon-gold/10 rounded-full blur-3xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 pointer-events-none"></div>
                            
                            <!-- Post Image with enhanced styling -->
                            <div class="relative h-52 overflow-hidden">
                                <?php if ($post['image_url']): ?>
                                    <img
                                        src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>"
                                        alt="<?= htmlspecialchars($post['title']) ?>"
                                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                        loading="lazy"
                                    >
                                    <!-- Enhanced gradient overlay -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-salon-black/90 via-salon-black/50 to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-500"></div>
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-secondary-700 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-salon-gold/60 animate-pulse-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Enhanced Post Content -->
                            <div class="p-6 flex flex-col flex-grow relative z-10">
                                <!-- Enhanced Meta Info with icons -->
                                <div class="flex items-center text-gray-400 text-sm mb-4 space-x-3">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= $post['publish_date'] ? date('M j, Y', strtotime($post['publish_date'])) : date('M j, Y', strtotime($post['created_at'])) ?>
                                    </span>
                                    <span class="text-salon-gold/50">•</span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= max(1, ceil(str_word_count(strip_tags($post['full_content'])) / 200)) ?> min read
                                    </span>
                                </div>

                                <!-- Enhanced Title with hover effect - Made entire area clickable -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>" class="block">
                                    <h3 class="text-xl font-bold text-white mb-3 line-clamp-2 group-hover:text-salon-gold transition-colors duration-300">
                                        <?= htmlspecialchars($post['title']) ?>
                                    </h3>
                                </a>

                                <!-- Enhanced Excerpt with better typography -->
                                <p class="text-gray-300 mb-6 line-clamp-3 flex-grow leading-relaxed">
                                    <?= !empty($post['summary']) ? htmlspecialchars($post['summary']) : htmlspecialchars(substr(strip_tags($post['full_content']), 0, 150)) . '...' ?>
                                </p>

                                <!-- Enhanced Read More Link with animation -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>"
                                   class="inline-flex items-center text-salon-gold hover:text-white font-semibold transition-all duration-300 group-hover:translate-x-2 transition-transform ease-in-out mt-auto bg-secondary-800/80 hover:bg-salon-gold/20 px-4 py-1.5 rounded-full">
                                    Read Full Story
                                    <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php else: ?>
                <!-- Enhanced No Posts Found -->
                <div class="text-center py-20 relative">
                    <!-- Decorative elements -->
                    <div class="absolute top-0 left-1/2 -translate-x-1/2 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-70 pointer-events-none"></div>
                    <div class="absolute -bottom-20 left-1/4 w-40 h-40 bg-salon-gold/5 rounded-full blur-3xl opacity-50 pointer-events-none"></div>
                    
                    <div class="max-w-md mx-auto relative z-10 bg-secondary-800/50 backdrop-blur-sm p-10 rounded-2xl border border-secondary-700/50 shadow-xl">
                        <!-- Decorative top border -->
                        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-salon-gold/30 to-transparent"></div>
                        
                        <svg class="w-24 h-24 text-salon-gold/40 mx-auto mb-6 animate-pulse-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                        
                        <h3 class="text-2xl font-bold text-white mb-4 relative inline-block">
                            <?= !empty($search) ? 'No posts found' : 'No blog posts available' ?>
                            <!-- Decorative underline -->
                            <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-salon-gold/30"></span>
                        </h3>
                        
                        <p class="text-gray-300 mb-8 leading-relaxed">
                            <?= !empty($search) ? "Try adjusting your search terms or browse all posts." : "We're working on creating amazing content for you. Check back soon!" ?>
                        </p>
                        
                        <?php if (!empty($search)): ?>
                            <a href="<?= getBasePath() ?>/blog" 
                               class="inline-flex items-center bg-salon-gold/90 hover:bg-salon-gold text-black px-6 py-3 rounded-full font-semibold transition-all duration-300 shadow-lg hover:shadow-salon-gold/30 group">
                                <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                View All Posts
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Enhanced Pagination with Improved Visual Design -->
            <?php if ($totalPages > 1): ?>
            <div class="mt-20 flex flex-col items-center space-y-8 relative">
                <!-- Decorative elements -->
                <div class="absolute -top-10 left-1/2 -translate-x-1/2 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-50 pointer-events-none"></div>
                
                <!-- Enhanced Pagination Info -->
                <div class="text-center relative z-10 bg-secondary-800/30 backdrop-blur-sm px-6 py-2 rounded-full border border-secondary-700/50">
                    <p class="text-gray-300">
                        Showing page <span class="text-salon-gold font-semibold"><?= $page ?></span>
                        of <span class="text-salon-gold font-semibold"><?= $totalPages ?></span>
                        <span class="mx-1 text-salon-gold/50">•</span>
                        <span class="text-gray-300"><?= $totalPosts ?> total <?= $totalPosts === 1 ? 'post' : 'posts' ?></span>
                    </p>
                </div>

                <!-- Enhanced Pagination Controls -->
                <nav class="flex flex-wrap items-center justify-center gap-2 relative z-10" aria-label="Pagination">
                    <?php
                    // Build pagination URL with search parameter
                    $baseUrl = getBasePath() . '/blog';
                    $searchParam = !empty($search) ? '&search=' . urlencode($search) : '';

                    // Calculate pagination range
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);

                    // Adjust range if we're near the beginning or end
                    if ($endPage - $startPage < 4) {
                        if ($startPage === 1) {
                            $endPage = min($totalPages, $startPage + 4);
                        } else {
                            $startPage = max(1, $endPage - 4);
                        }
                    }
                    ?>

                    <!-- Previous Button with Enhanced Styling -->
                    <?php if ($page > 1): ?>
                        <a href="<?= $baseUrl ?>?page=<?= $page - 1 ?><?= $searchParam ?>"
                           class="flex items-center px-5 py-2.5 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 group shadow-md hover:shadow-salon-gold/20">
                            <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </a>
                    <?php else: ?>
                        <span class="flex items-center px-5 py-2.5 text-sm font-medium text-gray-500 bg-secondary-800/30 border border-secondary-700/50 rounded-full cursor-not-allowed opacity-70">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </span>
                    <?php endif; ?>

                    <!-- First Page with Enhanced Styling -->
                    <?php if ($startPage > 1): ?>
                        <a href="<?= $baseUrl ?>?page=1<?= $searchParam ?>"
                           class="w-10 h-10 flex items-center justify-center text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 shadow-md hover:shadow-salon-gold/20">
                            1
                        </a>
                        <?php if ($startPage > 2): ?>
                            <span class="flex items-center justify-center text-salon-gold/70">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Page Numbers with Enhanced Styling -->
                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <?php if ($i === $page): ?>
                            <span class="w-10 h-10 flex items-center justify-center text-sm font-bold text-black bg-salon-gold rounded-full shadow-lg transform scale-110 transition-transform duration-300">
                                <?= $i ?>
                            </span>
                        <?php else: ?>
                            <a href="<?= $baseUrl ?>?page=<?= $i ?><?= $searchParam ?>"
                               class="w-10 h-10 flex items-center justify-center text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 shadow-md hover:shadow-salon-gold/20">
                                <?= $i ?>
                            </a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <!-- Last Page with Enhanced Styling -->
                    <?php if ($endPage < $totalPages): ?>
                        <?php if ($endPage < $totalPages - 1): ?>
                            <span class="flex items-center justify-center text-salon-gold/70">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        <?php endif; ?>
                        <a href="<?= $baseUrl ?>?page=<?= $totalPages ?><?= $searchParam ?>"
                           class="w-10 h-10 flex items-center justify-center text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 shadow-md hover:shadow-salon-gold/20">
                            <?= $totalPages ?>
                        </a>
                    <?php endif; ?>

                    <!-- Next Button with Enhanced Styling -->
                    <?php if ($page < $totalPages): ?>
                        <a href="<?= $baseUrl ?>?page=<?= $page + 1 ?><?= $searchParam ?>"
                           class="flex items-center px-5 py-2.5 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 group shadow-md hover:shadow-salon-gold/20">
                            Next
                            <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    <?php else: ?>
                        <span class="flex items-center px-5 py-2.5 text-sm font-medium text-gray-500 bg-secondary-800/30 border border-secondary-700/50 rounded-full cursor-not-allowed opacity-70">
                            Next
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </span>
                    <?php endif; ?>
                </nav>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Newsletter Subscription Section -->
<section class="py-20 bg-gradient-to-r from-salon-gold/10 via-salon-gold/5 to-transparent">
    <div class="max-w-4xl mx-auto px-6 text-center">
        <div class="bg-secondary-800/50 backdrop-blur-sm rounded-2xl border border-secondary-700/50 p-12">
            <div class="mb-8">
                <svg class="w-16 h-16 text-salon-gold mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-4">
                    Stay <span class="text-salon-gold">Beautiful</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Subscribe to our newsletter and never miss the latest beauty tips, trends, and exclusive offers.
                </p>
            </div>

            <form class="max-w-md mx-auto" action="<?= getBasePath() ?>/api/newsletter.php" method="POST">
                <div class="flex flex-col sm:flex-row gap-4">
                    <input
                        type="email"
                        name="email"
                        placeholder="Enter your email address"
                        class="flex-1 px-6 py-4 bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold focus:ring-2 focus:ring-salon-gold/20 transition-all duration-300"
                        required
                    >
                    <button
                        type="submit"
                        class="px-8 py-4 bg-salon-gold hover:bg-gold-light text-black font-semibold rounded-lg transition-all duration-300 hover:scale-105 whitespace-nowrap"
                    >
                        Subscribe
                    </button>
                </div>
                <p class="text-sm text-gray-400 mt-4">
                    We respect your privacy. Unsubscribe at any time.
                </p>
            </form>
        </div>
    </div>
</section>

<?php include __DIR__ . '/includes/footer.php'; ?>