<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME . ' Admin' : APP_NAME . ' - Admin Panel' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Admin panel for Flix Salon & SPA salon management system' ?>">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/flix_logo.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#1a1a1a',
                            800: '#141414',
                            900: '#0a0a0a',
                        },
                        'salon-black': '#000000',
                        'salon-gold': '#f59e0b',
                        'salon-white': '#ffffff',
                        'gold-light': '#fbbf24',
                        'gold-dark': '#d97706',
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --background: #000000;
            --foreground: #ffffff;
            --gold: #f59e0b;
            --gold-light: #fbbf24;
            --gold-dark: #d97706;
            --black-secondary: #0a0a0a;
            --black-tertiary: #141414;
            --black-quaternary: #1a1a1a;
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--black-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gold);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gold-dark);
        }

        /* Selection */
        ::selection {
            background: var(--gold);
            color: var(--background);
        }

        /* Glass effect for headers and cards */
        .glass-effect {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(245, 158, 11, 0.1);
        }

        /* Hover effects */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.2);
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen bg-salon-black admin-page">
    <!-- Admin Header -->
    <header class="bg-salon-black/98 backdrop-blur-md border-b border-secondary-700 fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Title -->
                <div class="flex items-center">
                    <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-salon-gold hover:bg-salon-gold/10 focus:outline-none focus:ring-2 focus:ring-salon-gold transition-all duration-300">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <div class="ml-4 lg:ml-0">
                        <h1 class="text-xl font-bold font-serif text-salon-gold">Flix Admin</h1>
                        <p class="text-sm text-gray-300">Salon Management System</p>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notificationButton" onclick="toggleNotifications()"
                                class="p-2 rounded-md text-gray-400 hover:text-white hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-salon-gold relative transition-colors">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                            </svg>
                            <span id="notificationCounter" class="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs font-medium flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- Notification Dropdown -->
                        <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-96 bg-secondary-800 rounded-lg shadow-xl border border-secondary-700 z-50 max-h-96 overflow-hidden">
                            <!-- Header -->
                            <div class="px-4 py-3 border-b border-secondary-700 flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-white">Notifications</h3>
                                <div class="flex items-center space-x-2">
                                    <button onclick="markAllAsRead()" class="text-xs text-salon-gold hover:text-gold-light transition-colors">
                                        Mark all read
                                    </button>
                                    <button onclick="openNotificationsPage()" class="text-xs text-gray-400 hover:text-white transition-colors">
                                        View all
                                    </button>
                                </div>
                            </div>

                            <!-- Category Tabs -->
                            <div class="px-4 py-2 border-b border-secondary-700">
                                <div class="flex space-x-1 text-xs">
                                    <button onclick="filterNotifications('all')" class="notification-tab active px-3 py-1 rounded-md bg-salon-gold text-black font-medium">
                                        All <span id="count-all">0</span>
                                    </button>
                                    <button onclick="filterNotifications('BOOKING')" class="notification-tab px-3 py-1 rounded-md text-gray-400 hover:text-white transition-colors">
                                        Bookings <span id="count-BOOKING">0</span>
                                    </button>
                                    <button onclick="filterNotifications('CUSTOMER')" class="notification-tab px-3 py-1 rounded-md text-gray-400 hover:text-white transition-colors">
                                        Customers <span id="count-CUSTOMER">0</span>
                                    </button>
                                    <button onclick="filterNotifications('SYSTEM')" class="notification-tab px-3 py-1 rounded-md text-gray-400 hover:text-white transition-colors">
                                        System <span id="count-SYSTEM">0</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Notifications List -->
                            <div id="notificationsList" class="max-h-64 overflow-y-auto">
                                <div class="p-4 text-center text-gray-400">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-salon-gold mx-auto mb-2"></div>
                                    Loading notifications...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="relative">
                        <button id="user-menu-toggle" class="flex items-center space-x-3 p-2 rounded-md text-gray-300 hover:text-white hover:bg-salon-gold/10 focus:outline-none focus:ring-2 focus:ring-salon-gold transition-all duration-300">
                            <div class="h-8 w-8 rounded-full bg-salon-gold flex items-center justify-center">
                                <span class="text-sm font-medium text-black">
                                    <?= strtoupper(substr(getCurrentUser()['name'], 0, 2)) ?>
                                </span>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-medium"><?= htmlspecialchars(getCurrentUser()['name']) ?></p>
                                <p class="text-xs text-gray-400">Administrator</p>
                            </div>
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-secondary-900/90 backdrop-blur-md border border-secondary-700 rounded-lg shadow-xl py-2 z-50">
                            <?php $basePath = getBasePath(); ?>
                            <a href="<?= $basePath ?>/admin/profile" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profile Settings
                            </a>
                            <a href="<?= $basePath ?>/admin/settings" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                System Settings
                            </a>
                            <div class="border-t border-secondary-700 my-1"></div>
                            <a href="<?= $basePath ?>/" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                View Website
                            </a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="block px-4 py-2 text-sm text-red-400 hover:bg-secondary-700 hover:text-red-300 transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Mobile Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-64 bg-secondary-800 lg:hidden">
        <div class="flex items-center justify-between p-4 border-b border-secondary-700">
            <h2 class="text-lg font-semibold text-salon-gold">Navigation</h2>
            <button id="mobile-sidebar-close" class="p-2 rounded-md text-gray-400 hover:text-white hover:bg-secondary-700">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-4">
            <?php include __DIR__ . '/admin_sidebar_nav.php'; ?>
        </nav>
    </div>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.remove('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.remove('hidden');
        });

        document.getElementById('mobile-sidebar-close').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        document.getElementById('mobile-sidebar-overlay').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        // User menu toggle
        document.getElementById('user-menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');
            
            if (!userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>
