<?php
/**
 * Admin Two-Factor Authentication (2FA) Functions
 * Flix Salonce - Enhanced Security System
 */

require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/email_functions.php';

/**
 * Check if admin has 2FA enabled
 */
function isAdmin2FAEnabled($adminId, $db = null) {
    global $database;

    try {
        // Use provided database connection or global one
        $dbConnection = $db ?: $database;

        // Check if database connection exists
        if (!$dbConnection) {
            error_log("Database connection not available in isAdmin2FAEnabled");
            return false;
        }

        $settings = $dbConnection->fetch(
            "SELECT is_enabled FROM admin_2fa_settings WHERE admin_id = ?",
            [$adminId]
        );

        return $settings ? (bool)$settings['is_enabled'] : false;

    } catch (Exception $e) {
        error_log("Error checking 2FA status for admin $adminId: " . $e->getMessage());
        return false;
    }
}

/**
 * Get admin 2FA settings
 */
function getAdmin2FASettings($adminId) {
    global $database;
    
    $settings = $database->fetch(
        "SELECT * FROM admin_2fa_settings WHERE admin_id = ?",
        [$adminId]
    );
    
    if (!$settings) {
        // Create default settings
        $settingsId = generateUUID();
        $database->query(
            "INSERT INTO admin_2fa_settings (id, admin_id, is_enabled, email_2fa_enabled, backup_codes_enabled) 
             VALUES (?, ?, 0, 0, 0)",
            [$settingsId, $adminId]
        );
        
        return [
            'id' => $settingsId,
            'admin_id' => $adminId,
            'is_enabled' => false,
            'email_2fa_enabled' => false,
            'backup_codes_enabled' => false,
            'backup_codes_generated_at' => null
        ];
    }
    
    return $settings;
}

/**
 * Generate 6-digit verification code
 */
function generate2FACode() {
    return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Generate backup codes
 */
function generateBackupCodes($count = 10) {
    $codes = [];
    for ($i = 0; $i < $count; $i++) {
        // Generate 8-character alphanumeric codes
        $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
    }
    return $codes;
}

/**
 * Send email verification code
 */
function sendAdmin2FAEmailCode($adminId) {
    global $database;
    
    // Check rate limiting
    if (!checkAdmin2FARateLimit($adminId, 'EMAIL_CODE')) {
        return ['success' => false, 'error' => 'Too many attempts. Please wait before requesting another code.'];
    }
    
    // Get admin details
    $admin = $database->fetch(
        "SELECT name, email FROM users WHERE id = ? AND role = 'ADMIN'",
        [$adminId]
    );
    
    if (!$admin) {
        return ['success' => false, 'error' => 'Admin not found'];
    }
    
    // Invalidate any existing codes
    $database->query(
        "UPDATE admin_2fa_email_codes SET is_used = 1 WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );
    
    // Generate new code
    $code = generate2FACode();
    $codeId = generateUUID();
    $expiresAt = date('Y-m-d H:i:s', strtotime('+10 minutes'));
    
    // Store code in database
    $database->query(
        "INSERT INTO admin_2fa_email_codes (id, admin_id, code, expires_at, ip_address, user_agent) 
         VALUES (?, ?, ?, ?, ?, ?)",
        [
            $codeId,
            $adminId,
            $code,
            $expiresAt,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]
    );
    
    // Send email
    $emailSent = sendAdminEmail(
        $admin['email'],
        'Two-Factor Authentication Code - Flix Salonce',
        'admin_2fa_email_code',
        [
            'admin_name' => $admin['name'],
            'verification_code' => $code,
            'expires_minutes' => 10,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ]
    );
    
    if ($emailSent) {
        // Log the action
        log2FAAction($adminId, '2FA_EMAIL_SENT', 'Email verification code sent', [
            'code_id' => $codeId,
            'expires_at' => $expiresAt
        ]);
        
        return ['success' => true, 'expires_at' => $expiresAt];
    } else {
        // Delete the code if email failed
        $database->query("DELETE FROM admin_2fa_email_codes WHERE id = ?", [$codeId]);
        return ['success' => false, 'error' => 'Failed to send verification email'];
    }
}

/**
 * Verify email 2FA code
 */
function verifyAdmin2FAEmailCode($adminId, $code) {
    global $database;
    
    // Check rate limiting
    if (!checkAdmin2FARateLimit($adminId, 'EMAIL_CODE')) {
        return ['success' => false, 'error' => 'Too many attempts. Please wait before trying again.'];
    }
    
    // Find valid code
    $codeRecord = $database->fetch(
        "SELECT * FROM admin_2fa_email_codes 
         WHERE admin_id = ? AND code = ? AND is_used = 0 AND expires_at > NOW()
         ORDER BY created_at DESC LIMIT 1",
        [$adminId, $code]
    );
    
    // Log attempt
    logAdmin2FAAttempt($adminId, 'EMAIL_CODE', $codeRecord ? true : false);
    
    if (!$codeRecord) {
        log2FAAction($adminId, '2FA_EMAIL_FAILED', 'Invalid or expired email verification code', [
            'attempted_code' => $code
        ]);
        return ['success' => false, 'error' => 'Invalid or expired verification code'];
    }
    
    // Mark code as used
    $database->query(
        "UPDATE admin_2fa_email_codes SET is_used = 1, attempts = attempts + 1 WHERE id = ?",
        [$codeRecord['id']]
    );
    
    // Log successful verification
    log2FAAction($adminId, '2FA_EMAIL_SUCCESS', 'Email verification code verified successfully', [
        'code_id' => $codeRecord['id']
    ]);
    
    return ['success' => true];
}

/**
 * Store backup codes for admin
 */
function storeAdminBackupCodes($adminId, $codes, $db = null) {
    global $database;

    // Use provided database connection or fall back to global
    $dbConnection = $db ?: $database;

    try {
        error_log("storeAdminBackupCodes: Starting for admin $adminId with " . count($codes) . " codes");

        // Validate inputs
        if (empty($adminId) || empty($codes) || !is_array($codes)) {
            throw new Exception("Invalid parameters for backup codes storage");
        }

        // Ensure database connection exists
        if (!$dbConnection) {
            throw new Exception("Database connection not available");
        }

        // Test database connection with a simple query
        try {
            $testQuery = $dbConnection->fetch("SELECT 1 as test");
            if (!$testQuery) {
                throw new Exception("Database connection test failed");
            }
            error_log("storeAdminBackupCodes: Database connection verified");
        } catch (Exception $e) {
            throw new Exception("Database connection error: " . $e->getMessage());
        }

        // Ensure generateUUID function is available
        if (!function_exists('generateUUID')) {
            throw new Exception("generateUUID function not available");
        }

        // Start transaction for atomic operation
        $dbConnection->beginTransaction();
        error_log("storeAdminBackupCodes: Transaction started");

        // Delete existing backup codes
        error_log("storeAdminBackupCodes: Deleting existing backup codes");
        try {
            $deleteResult = $dbConnection->query("DELETE FROM admin_2fa_backup_codes WHERE admin_id = ?", [$adminId]);
            error_log("storeAdminBackupCodes: Delete operation completed");
        } catch (Exception $e) {
            throw new Exception("Failed to delete existing backup codes: " . $e->getMessage());
        }

        // Store new backup codes
        error_log("storeAdminBackupCodes: Storing new backup codes");
        foreach ($codes as $index => $code) {
            try {
                $codeId = generateUUID();
                $codeHash = password_hash($code, PASSWORD_DEFAULT);

                $insertResult = $dbConnection->query(
                    "INSERT INTO admin_2fa_backup_codes (id, admin_id, code_hash) VALUES (?, ?, ?)",
                    [$codeId, $adminId, $codeHash]
                );

                error_log("storeAdminBackupCodes: Stored code " . ($index + 1) . " with ID: $codeId");
            } catch (Exception $e) {
                throw new Exception("Failed to insert backup code " . ($index + 1) . ": " . $e->getMessage());
            }
        }

        // Update settings - ensure 2FA settings record exists first
        error_log("storeAdminBackupCodes: Checking for existing 2FA settings");
        $existingSettings = $dbConnection->fetch("SELECT id FROM admin_2fa_settings WHERE admin_id = ?", [$adminId]);

        if ($existingSettings) {
            error_log("storeAdminBackupCodes: Updating existing 2FA settings");
            try {
                $updateResult = $dbConnection->query(
                    "UPDATE admin_2fa_settings SET backup_codes_enabled = 1, backup_codes_generated_at = NOW()
                     WHERE admin_id = ?",
                    [$adminId]
                );
                error_log("storeAdminBackupCodes: Updated existing 2FA settings");
            } catch (Exception $e) {
                throw new Exception("Failed to update 2FA settings: " . $e->getMessage());
            }
        } else {
            // Create settings record if it doesn't exist
            error_log("storeAdminBackupCodes: Creating new 2FA settings record");
            try {
                $settingsId = generateUUID();
                $insertResult = $dbConnection->query(
                    "INSERT INTO admin_2fa_settings (id, admin_id, is_enabled, email_2fa_enabled, backup_codes_enabled, backup_codes_generated_at)
                     VALUES (?, ?, 0, 0, 1, NOW())",
                    [$settingsId, $adminId]
                );
                error_log("storeAdminBackupCodes: Created 2FA settings with ID: $settingsId");
            } catch (Exception $e) {
                throw new Exception("Failed to create 2FA settings record: " . $e->getMessage());
            }
        }

        // Log the action inside the transaction
        try {
            $logId = generateUUID();
            $dbConnection->query(
                "INSERT INTO admin_2fa_logs (id, admin_id, action, description, metadata, ip_address, user_agent)
                 VALUES (?, ?, ?, ?, ?, ?, ?)",
                [
                    $logId,
                    $adminId,
                    '2FA_BACKUP_GENERATED',
                    'Backup codes generated',
                    json_encode(['codes_count' => count($codes)]),
                    $_SERVER['REMOTE_ADDR'] ?? null,
                    $_SERVER['HTTP_USER_AGENT'] ?? null
                ]
            );
            error_log("storeAdminBackupCodes: Logged action with ID: $logId");
        } catch (Exception $e) {
            error_log("storeAdminBackupCodes: Failed to log action: " . $e->getMessage());
            // Don't fail the whole operation for logging issues
        }

        // Commit transaction
        $dbConnection->commit();
        error_log("storeAdminBackupCodes: Transaction committed successfully");

        return ['success' => true];

    } catch (Exception $e) {
        // Rollback transaction on error
        try {
            $dbConnection->rollback();
            error_log("storeAdminBackupCodes: Transaction rolled back due to error");
        } catch (Exception $rollbackError) {
            error_log("storeAdminBackupCodes: Failed to rollback transaction: " . $rollbackError->getMessage());
        }

        error_log("Failed to store backup codes for admin $adminId: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to store backup codes: ' . $e->getMessage()];
    }
}

/**
 * Verify backup code
 */
function verifyAdminBackupCode($adminId, $code) {
    global $database;
    
    // Check rate limiting
    if (!checkAdmin2FARateLimit($adminId, 'BACKUP_CODE')) {
        return ['success' => false, 'error' => 'Too many attempts. Please wait before trying again.'];
    }
    
    // Get unused backup codes
    $backupCodes = $database->fetchAll(
        "SELECT * FROM admin_2fa_backup_codes WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );
    
    $isValid = false;
    $usedCodeId = null;
    
    foreach ($backupCodes as $backupCode) {
        if (password_verify($code, $backupCode['code_hash'])) {
            $isValid = true;
            $usedCodeId = $backupCode['id'];
            break;
        }
    }
    
    // Log attempt
    logAdmin2FAAttempt($adminId, 'BACKUP_CODE', $isValid);
    
    if (!$isValid) {
        log2FAAction($adminId, '2FA_BACKUP_FAILED', 'Invalid backup code attempted', [
            'attempted_code' => substr($code, 0, 2) . '****'
        ]);
        return ['success' => false, 'error' => 'Invalid backup code'];
    }
    
    // Mark backup code as used
    $database->query(
        "UPDATE admin_2fa_backup_codes SET is_used = 1, used_at = NOW(), used_ip = ? WHERE id = ?",
        [$_SERVER['REMOTE_ADDR'] ?? null, $usedCodeId]
    );
    
    // Check remaining backup codes
    $remainingCodes = $database->fetch(
        "SELECT COUNT(*) as count FROM admin_2fa_backup_codes WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );
    
    log2FAAction($adminId, '2FA_BACKUP_SUCCESS', 'Backup code used successfully', [
        'code_id' => $usedCodeId,
        'remaining_codes' => $remainingCodes['count']
    ]);
    
    return [
        'success' => true,
        'remaining_codes' => $remainingCodes['count']
    ];
}

/**
 * Check rate limiting for 2FA attempts
 */
function checkAdmin2FARateLimit($adminId, $attemptType, $maxAttempts = 5, $timeWindow = 900) {
    global $database;

    $since = date('Y-m-d H:i:s', time() - $timeWindow);

    $attempts = $database->fetch(
        "SELECT COUNT(*) as count FROM admin_2fa_attempts
         WHERE (admin_id = ? OR ip_address = ?) AND attempt_type = ? AND attempted_at > ?",
        [$adminId, $_SERVER['REMOTE_ADDR'] ?? '', $attemptType, $since]
    );

    return $attempts['count'] < $maxAttempts;
}

/**
 * Log 2FA attempt
 */
function logAdmin2FAAttempt($adminId, $attemptType, $isSuccessful) {
    global $database;

    try {
        $database->query(
            "INSERT INTO admin_2fa_attempts (id, admin_id, ip_address, attempt_type, is_successful)
             VALUES (?, ?, ?, ?, ?)",
            [
                generateUUID(),
                $adminId,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $attemptType,
                $isSuccessful ? 1 : 0
            ]
        );
    } catch (Exception $e) {
        error_log("Failed to log 2FA attempt: " . $e->getMessage());
    }
}

/**
 * Log 2FA action for audit trail
 */
function log2FAAction($adminId, $action, $description, $metadata = null) {
    global $database;

    try {
        $database->query(
            "INSERT INTO admin_2fa_logs (id, admin_id, action, description, metadata, ip_address, user_agent)
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            [
                generateUUID(),
                $adminId,
                $action,
                $description,
                $metadata ? json_encode($metadata) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]
        );
    } catch (Exception $e) {
        error_log("Failed to log 2FA action: " . $e->getMessage());
    }
}

/**
 * Enable 2FA for admin
 */
function enableAdmin2FA($adminId, $emailEnabled = true, $backupCodesEnabled = true) {
    global $database;

    try {
        $database->query(
            "UPDATE admin_2fa_settings
             SET is_enabled = 1, email_2fa_enabled = ?, backup_codes_enabled = ?, updated_at = NOW()
             WHERE admin_id = ?",
            [$emailEnabled ? 1 : 0, $backupCodesEnabled ? 1 : 0, $adminId]
        );

        log2FAAction($adminId, '2FA_ENABLED', 'Two-factor authentication enabled', [
            'email_enabled' => $emailEnabled,
            'backup_codes_enabled' => $backupCodesEnabled
        ]);

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Failed to enable 2FA: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to enable 2FA'];
    }
}

/**
 * Disable 2FA for admin
 */
function disableAdmin2FA($adminId) {
    global $database;

    try {
        // Disable 2FA
        $database->query(
            "UPDATE admin_2fa_settings
             SET is_enabled = 0, email_2fa_enabled = 0, backup_codes_enabled = 0, updated_at = NOW()
             WHERE admin_id = ?",
            [$adminId]
        );

        // Invalidate existing codes and backup codes
        $database->query("UPDATE admin_2fa_email_codes SET is_used = 1 WHERE admin_id = ?", [$adminId]);
        $database->query("DELETE FROM admin_2fa_backup_codes WHERE admin_id = ?", [$adminId]);

        log2FAAction($adminId, '2FA_DISABLED', 'Two-factor authentication disabled');

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Failed to disable 2FA: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to disable 2FA'];
    }
}

/**
 * Check if session requires 2FA verification
 */
function sessionRequires2FA() {
    return isset($_SESSION['requires_2fa']) && $_SESSION['requires_2fa'] === true;
}

/**
 * Check if session is 2FA verified
 */
function sessionIs2FAVerified() {
    return isset($_SESSION['is_2fa_verified']) && $_SESSION['is_2fa_verified'] === true;
}

/**
 * Mark session as requiring 2FA
 */
function markSessionRequires2FA($adminId) {
    $_SESSION['requires_2fa'] = true;
    $_SESSION['is_2fa_verified'] = false;
    $_SESSION['pending_2fa_admin_id'] = $adminId;
}

/**
 * Mark session as 2FA verified
 */
function markSession2FAVerified() {
    $_SESSION['requires_2fa'] = false;
    $_SESSION['is_2fa_verified'] = true;
    unset($_SESSION['pending_2fa_admin_id']);
}

/**
 * Clear 2FA session data
 */
function clear2FASessionData() {
    unset($_SESSION['requires_2fa']);
    unset($_SESSION['is_2fa_verified']);
    unset($_SESSION['pending_2fa_admin_id']);
}

/**
 * Get remaining backup codes count
 */
function getRemainingBackupCodesCount($adminId) {
    global $database;

    $result = $database->fetch(
        "SELECT COUNT(*) as count FROM admin_2fa_backup_codes WHERE admin_id = ? AND is_used = 0",
        [$adminId]
    );

    return $result['count'];
}

/**
 * Clean up expired 2FA sessions and codes
 */
function cleanup2FAExpiredData() {
    global $database;

    try {
        // Clean up expired email codes (older than 10 minutes)
        $database->query(
            "DELETE FROM admin_2fa_email_codes WHERE created_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)"
        );

        // Clean up old rate limiting attempts (older than 24 hours)
        $database->query(
            "DELETE FROM admin_2fa_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)"
        );

        // Clean up old audit logs (older than 90 days)
        $database->query(
            "DELETE FROM admin_2fa_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)"
        );

        return true;

    } catch (Exception $e) {
        error_log("Failed to cleanup 2FA expired data: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if admin session requires 2FA verification
 */
function requiresAdmin2FAVerification() {
    return isset($_SESSION['requires_2fa']) && $_SESSION['requires_2fa'] &&
           (!isset($_SESSION['is_2fa_verified']) || !$_SESSION['is_2fa_verified']);
}

/**
 * Get 2FA session status
 */
function get2FASessionStatus() {
    if (!isset($_SESSION['requires_2fa']) || !$_SESSION['requires_2fa']) {
        return ['status' => 'none', 'message' => 'No 2FA required'];
    }

    if (isset($_SESSION['2fa_expires']) && time() > $_SESSION['2fa_expires']) {
        return ['status' => 'expired', 'message' => '2FA session expired'];
    }

    if (!isset($_SESSION['is_2fa_verified']) || !$_SESSION['is_2fa_verified']) {
        return ['status' => 'pending', 'message' => '2FA verification required'];
    }

    return ['status' => 'verified', 'message' => '2FA verification complete'];
}

/**
 * Send admin email using template
 */
function sendAdminEmail($to, $subject, $template, $data = []) {
    // Include email functions if not already loaded
    if (!function_exists('sendSMTPEmail')) {
        require_once __DIR__ . '/email_functions.php';
    }

    $templates = [
        'admin_2fa_email_code' => "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>Two-Factor Authentication Code</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #1f2937, #374151); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
                    .code-box { background: #1f2937; color: #f59e0b; font-size: 32px; font-weight: bold; text-align: center; padding: 20px; margin: 20px 0; border-radius: 8px; letter-spacing: 4px; }
                    .security-info { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 8px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class='header'>
                    <h1>🔐 Two-Factor Authentication</h1>
                    <p>Flix Salon & SPA - Admin Access</p>
                </div>
                <div class='content'>
                    <p>Hello <strong>{admin_name}</strong>,</p>
                    <p>Your verification code for Flix Salon & SPA admin access is:</p>
                    <div class='code-box'>{verification_code}</div>
                    <p><strong>⏰ This code will expire in {expires_minutes} minutes.</strong></p>

                    <div class='security-info'>
                        <h4>🛡️ Security Information:</h4>
                        <ul>
                            <li><strong>IP Address:</strong> {ip_address}</li>
                            <li><strong>Time:</strong> {timestamp}</li>
                        </ul>
                        <p><strong>⚠️ If you did not request this code, please contact support immediately.</strong></p>
                    </div>

                    <p>For security reasons, do not share this code with anyone.</p>
                </div>
                <div class='footer'>
                    <p>Best regards,<br><strong>Flix Salon & SPA Team</strong></p>
                    <p>📧 <EMAIL> | 📞 +255 781 985 757</p>
                </div>
            </body>
            </html>
        "
    ];

    if (!isset($templates[$template])) {
        error_log("Email template '$template' not found");
        return false;
    }

    $body = $templates[$template];
    foreach ($data as $key => $value) {
        $body = str_replace('{' . $key . '}', $value, $body);
    }

    // Use the existing SMTP email service
    try {
        $result = sendSMTPEmail($to, $subject, $body, [
            'is_html' => true,
            'from_name' => 'Flix Salon & SPA - Security',
            'from_email' => SMTP_FROM_EMAIL
        ]);

        if ($result) {
            error_log("2FA email sent successfully to: $to");
        } else {
            error_log("Failed to send 2FA email to: $to");
        }

        return $result;
    } catch (Exception $e) {
        error_log("Error sending 2FA email to $to: " . $e->getMessage());
        return false;
    }
}
?>
