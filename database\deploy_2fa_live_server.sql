-- Two-Factor Authentication (2FA) Database Schema for Live Server
-- Flix Salonce - Admin 2FA System
-- CORRECTED VERSION: References users table (not admins table)
-- FIXED: Ensures exact data type compatibility with users table

-- Drop existing tables if they exist (to fix any data type mismatches)
DROP TABLE IF EXISTS admin_2fa_logs;
DROP TABLE IF EXISTS admin_2fa_attempts;
DROP TABLE IF EXISTS admin_2fa_backup_codes;
DROP TABLE IF EXISTS admin_2fa_email_codes;
DROP TABLE IF EXISTS admin_2fa_settings;

-- Table for storing 2FA settings for admin users
CREATE TABLE admin_2fa_settings (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL UNIQUE,
    is_enabled TINYINT(1) DEFAULT 0,
    email_2fa_enabled TINYINT(1) DEFAULT 0,
    backup_codes_enabled TINYINT(1) DEFAULT 0,
    backup_codes_generated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_admin_id (admin_id),
    CONSTRAINT admin_2fa_settings_admin_fk FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for storing email verification codes
CREATE TABLE admin_2fa_email_codes (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    attempts INT DEFAULT 0,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_email_admin_id (admin_id),
    INDEX idx_admin_2fa_email_expires (expires_at),
    INDEX idx_admin_2fa_email_code (code),
    CONSTRAINT admin_2fa_email_codes_admin_fk FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for storing backup codes
CREATE TABLE admin_2fa_backup_codes (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code_hash VARCHAR(255) NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    used_at TIMESTAMP NULL,
    used_ip VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_backup_admin_id (admin_id),
    INDEX idx_admin_2fa_backup_used (is_used),
    CONSTRAINT admin_2fa_backup_codes_admin_fk FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for 2FA attempt rate limiting
CREATE TABLE admin_2fa_attempts (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    admin_id VARCHAR(36) NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_type ENUM('EMAIL_CODE', 'BACKUP_CODE') NOT NULL,
    is_successful TINYINT(1) DEFAULT 0,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_attempts_admin_id (admin_id),
    INDEX idx_admin_2fa_attempts_ip (ip_address),
    INDEX idx_admin_2fa_attempts_time (attempted_at),
    CONSTRAINT admin_2fa_attempts_admin_fk FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table for 2FA audit logs
CREATE TABLE admin_2fa_logs (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    metadata JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_logs_admin_id (admin_id),
    INDEX idx_admin_2fa_logs_action (action),
    INDEX idx_admin_2fa_logs_created_at (created_at),
    CONSTRAINT admin_2fa_logs_admin_fk FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample data or verify existing admin users
-- This will show which admin users exist in your system
SELECT id, name, email, role FROM users WHERE role = 'ADMIN';

-- Optional: Enable 2FA for existing admin users (uncomment if needed)
-- INSERT IGNORE INTO admin_2fa_settings (id, admin_id, is_enabled, email_2fa_enabled) 
-- SELECT UUID(), id, 0, 1 FROM users WHERE role = 'ADMIN';
