<?php
/**
 * Debug Session Information
 */

// Set JSON response header
header('Content-Type: application/json');

// Start output buffering
ob_start();

try {
    require_once __DIR__ . '/../../config/app.php';
    
    // Clean any output buffer
    ob_clean();
    
    // Return session debug information
    echo json_encode([
        'success' => true,
        'session_id' => session_id(),
        'session_data' => $_SESSION,
        'user_id' => $_SESSION['user_id'] ?? null,
        'user_role' => $_SESSION['user_role'] ?? null,
        'is_logged_in' => isLoggedIn(),
        'session_status' => session_status(),
        'session_name' => session_name(),
        'cookie_params' => session_get_cookie_params()
    ]);
    
} catch (Exception $e) {
    // Clean any output buffer
    ob_clean();
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'session_id' => session_id() ?? 'none',
        'session_status' => session_status()
    ]);
}

// End output buffering
ob_end_flush();
?>
