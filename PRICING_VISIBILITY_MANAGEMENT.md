# Pricing Visibility Management System

## 🎯 QUICK MANAGEMENT GUIDE

### To SHOW pricing to all users again:
1. **Edit `includes/functions.php`**
2. **Find the `shouldShowPricing()` function (around line 95)**
3. **Change this:**
   ```php
   function shouldShowPricing() {
       if (!isLoggedIn()) return false;
       return $_SESSION['user_role'] === 'ADMIN';
   }
   ```
   **To this:**
   ```php
   function shouldShowPricing() {
       return true; // Show pricing to everyone
   }
   ```

### To HIDE pricing from all users:
1. **Edit `includes/functions.php`**
2. **Change the `shouldShowPricing()` function to:**
   ```php
   function shouldShowPricing() {
       return false; // Hide pricing from everyone
   }
   ```

### To show pricing to CUSTOMERS and ADMINS only:
1. **Edit `includes/functions.php`**
2. **Change the `shouldShowPricing()` function to:**
   ```php
   function shouldShowPricing() {
       if (!isLoggedIn()) return false;
       return in_array($_SESSION['user_role'], ['ADMIN', 'CUSTOMER']);
   }
   ```

### To show pricing to LOGGED-IN users only:
1. **Edit `includes/functions.php`**
2. **Change the `shouldShowPricing()` function to:**
   ```php
   function shouldShowPricing() {
       return isLoggedIn(); // Show to any logged-in user
   }
   ```

## 📋 Overview
This document provides comprehensive documentation for the role-based pricing visibility system implemented across the Flix Salon & SPA management system. This system allows you to easily control who can see pricing information throughout the entire application.

## Implementation Date
**Date:** 2025-07-04  
**Scope:** Company-wide policy change to hide pricing from public and customer views

## Core Principle
- **ADMIN users:** Can see all pricing information (services, packages, payments)
- **CUSTOMER, STAFF, GUEST users:** Cannot see any pricing information
- **Backend functionality:** All payment processing and pricing calculations remain intact
- **Display layer only:** Only the visual display of pricing is affected

## 🔧 How It Works

### Central Control Function
The entire pricing visibility system is controlled by **ONE FUNCTION** in `includes/functions.php`:

```php
function shouldShowPricing() {
    if (!isLoggedIn()) return false;
    return $_SESSION['user_role'] === 'ADMIN';
}
```

**This single function controls pricing visibility across:**
- All public pages (index.php, services.php, packages.php)
- Customer panel pages
- API endpoints
- JavaScript functions
- Booking flows
- Payment displays

### Helper Functions
Additional helper functions work together:

```php
function displayPrice($amount, $alternativeText = 'Contact for pricing', $forceDisplay = false) {
    if (!$forceDisplay && !shouldShowPricing()) {
        return htmlspecialchars($alternativeText);
    }
    return formatCurrency($amount, null, true);
}

function shouldShowPriceFilters() {
    return shouldShowPricing();
}
```

## 📁 Files Modified

### Core Files
- **`includes/functions.php`** - Central control functions
- **`includes/service_card.php`** - Service display component

### Public Pages
- **`index.php`** - Homepage featured services
- **`services.php`** - Services listing and variation modals
- **`packages.php`** - Package listings

### Customer Panel
- **`customer/book/index.php`** - Booking interface
- **`customer/bookings/index.php`** - Booking history
- **`customer/payments/index.php`** - Payment interface

### API Endpoints
- **`api/customer/search-services.php`** - Service search API
- **`api/services.php`** - Services API
- **`api/customer/service_variations.php`** - Variations API
- **`api/admin/packages.php`** - Packages API

## 🔄 Common Scenarios

### Scenario 1: Show pricing to everyone (original behavior)
```php
function shouldShowPricing() {
    return true;
}
```

### Scenario 2: Hide pricing from everyone
```php
function shouldShowPricing() {
    return false;
}
```

### Scenario 3: Show pricing only to logged-in users
```php
function shouldShowPricing() {
    return isLoggedIn();
}
```

### Scenario 4: Show pricing to customers and admins
```php
function shouldShowPricing() {
    if (!isLoggedIn()) return false;
    return in_array($_SESSION['user_role'], ['ADMIN', 'CUSTOMER']);
}
```

### Scenario 5: Show pricing to staff and admins
```php
function shouldShowPricing() {
    if (!isLoggedIn()) return false;
    return in_array($_SESSION['user_role'], ['ADMIN', 'STAFF']);
}
```

## 🛠️ Advanced Customization

### Time-Based Pricing Visibility
```php
function shouldShowPricing() {
    // Hide pricing during business hours (9 AM - 6 PM)
    $hour = date('H');
    if ($hour >= 9 && $hour < 18) {
        return false;
    }
    return true;
}
```

### Location-Based Pricing Visibility
```php
function shouldShowPricing() {
    // Show pricing only to specific IP ranges
    $userIP = $_SERVER['REMOTE_ADDR'];
    $allowedIPs = ['192.168.1.', '10.0.0.'];
    
    foreach ($allowedIPs as $allowedIP) {
        if (strpos($userIP, $allowedIP) === 0) {
            return true;
        }
    }
    return false;
}
```

## 🔍 Testing Your Changes

### Quick Test
1. **Edit `includes/functions.php`**
2. **Modify the `shouldShowPricing()` function**
3. **Visit these pages to test:**
   - Homepage: `yoursite.com/`
   - Services: `yoursite.com/services.php`
   - Packages: `yoursite.com/packages.php`
   - Customer booking: `yoursite.com/customer/book/`

### Comprehensive Test
Run the test file: `yoursite.com/test_pricing_visibility.php`

## ⚠️ Important Notes

### What's Safe to Change
- **✅ The `shouldShowPricing()` function** - This is the main control
- **✅ The alternative text** in `displayPrice()` function
- **✅ Role-based conditions** in `shouldShowPricing()`

### What NOT to Change
- **❌ Payment processing logic** - This must remain intact
- **❌ Database pricing data** - Never modify actual price values
- **❌ Admin panel pricing** - Admins should always see pricing
- **❌ Backend calculations** - These must use real prices

### Backup Recommendation
Before making changes, backup your `includes/functions.php` file:
```bash
cp includes/functions.php includes/functions.php.backup
```

## 🔄 Reverting Changes

### To completely revert to original behavior:
1. **Edit `includes/functions.php`**
2. **Change `shouldShowPricing()` to:**
   ```php
   function shouldShowPricing() {
       return true; // Original behavior - show to everyone
   }
   ```

### Emergency Revert
If something breaks, replace the entire function with:
```php
function shouldShowPricing() {
    return true;
}

function displayPrice($amount, $alternativeText = 'Contact for pricing', $forceDisplay = false) {
    return formatCurrency($amount, null, true);
}

function shouldShowPriceFilters() {
    return true;
}
```

## 📞 Support

If you need help or encounter issues:
1. Check the test file: `test_pricing_visibility.php`
2. Review the error logs in `/logs/error.log`
3. Ensure the `shouldShowPricing()` function syntax is correct
4. Test with a simple `return true;` first

## 🐛 Recent Bug Fixes (July 4, 2025)

### Issue: "TSH 0" Showing Instead of Hidden Pricing

**Problem:** Some JavaScript code was displaying "TSH 0" instead of hiding pricing completely.

**Root Cause:** JavaScript template literals in PHP files were not checking user roles before displaying pricing.

### Fixed Locations:

#### 1. Customer Booking Page (`customer/book/index.php`)
**Lines 1866-1870:** Service price display in filter results
```javascript
// BEFORE (showing TSH 0):
const priceDisplay = hasVariations ? 'Multiple Options' : `<?= $safeCurrencySymbol ?> ${parseFloat(service.price).toLocaleString()}`;

// AFTER (role-based):
<?php if (shouldShowPricing()): ?>
const priceDisplay = hasVariations ? 'Multiple Options' : `<?= $safeCurrencySymbol ?> ${parseFloat(service.price).toLocaleString()}`;
<?php else: ?>
const priceDisplay = 'Contact for pricing';
<?php endif; ?>
```

**Lines 1882-1886:** Service variation prices in filter results
```javascript
// BEFORE (showing TSH 0):
<span class="text-salon-gold"><?= $safeCurrencySymbol ?> ${parseFloat(variation.price).toLocaleString()}</span>

// AFTER (role-based):
<?php if (shouldShowPricing()): ?>
<span class="text-salon-gold"><?= $safeCurrencySymbol ?> ${parseFloat(variation.price).toLocaleString()}</span>
<?php else: ?>
<span class="text-salon-gold">Contact for pricing</span>
<?php endif; ?>
```

**Lines 1925-1932:** Package prices in filter results
```javascript
// BEFORE (showing TSH 0):
<span class="text-salon-gold font-semibold"><?= $safeCurrencySymbol ?> ${parseFloat(package.price).toLocaleString()}</span>

// AFTER (role-based):
<?php if (shouldShowPricing()): ?>
<span class="text-salon-gold font-semibold"><?= $safeCurrencySymbol ?> ${parseFloat(package.price).toLocaleString()}</span>
<?php else: ?>
<span class="text-salon-gold font-semibold">Contact for pricing</span>
<?php endif; ?>
```

#### 2. Services Page (`services.php`)
**Lines 1160:** Service variation modal pricing
```javascript
// BEFORE (showing TSH 0):
<span class="text-2xl font-bold text-salon-gold"><?= CURRENCY_SYMBOL ?> ${parseFloat(variation.price).toLocaleString()}</span>

// AFTER (role-based):
<?php if (shouldShowPricing()): ?>
<span class="text-2xl font-bold text-salon-gold"><?= CURRENCY_SYMBOL ?> ${parseFloat(variation.price).toLocaleString()}</span>
<?php else: ?>
<span class="text-2xl font-bold text-salon-gold">Contact for pricing</span>
<?php endif; ?>
```

### Why This Happened
1. **API endpoints** were correctly filtering prices to `0` for non-admin users
2. **JavaScript code** was still formatting and displaying these `0` values as "TSH 0"
3. **Solution:** Wrap JavaScript price displays with PHP role-checking conditions

### How to Prevent Similar Issues
When adding new JavaScript price displays:
1. **Always wrap price displays** with `<?php if (shouldShowPricing()): ?>`
2. **Provide alternative text** for non-admin users
3. **Test with non-admin accounts** to ensure "TSH 0" doesn't appear

### Testing the Fixes
1. **Log out or use customer account**
2. **Visit `/customer/book/` and apply filters**
3. **Visit `/services.php` and open variation modals**
4. **Verify:** Should see "Contact for pricing" instead of "TSH 0"

## 📝 Change Log

### July 4, 2025
- **Fixed:** Customer booking page showing "TSH 0" in filter results
- **Fixed:** Services page variation modal showing "TSH 0"
- **Added:** Comprehensive management documentation
- **Status:** All known pricing visibility issues resolved

### June 30, 2025
- **Implemented:** Initial role-based pricing visibility system
- **Modified:** 15+ files across frontend, backend, and API layers
- **Created:** Helper functions for centralized control

---

**Remember: The entire system is controlled by ONE function in `includes/functions.php`. Change that function to change the entire pricing visibility behavior across the application.**
