-- Two-Factor Authentication (2FA) Database Schema
-- Flix Salonce - Admin 2FA System

-- Table for storing 2FA settings for admin users
CREATE TABLE IF NOT EXISTS admin_2fa_settings (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL UNIQUE,
    is_enabled TINYINT(1) DEFAULT 0,
    email_2fa_enabled TINYINT(1) DEFAULT 0,
    backup_codes_enabled TINYINT(1) DEFAULT 0,
    backup_codes_generated_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_admin_id (admin_id),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for storing email verification codes
CREATE TABLE IF NOT EXISTS admin_2fa_email_codes (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    attempts INT DEFAULT 0,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_email_admin_id (admin_id),
    INDEX idx_admin_2fa_email_expires (expires_at),
    INDEX idx_admin_2fa_email_code (code),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for storing backup codes
CREATE TABLE IF NOT EXISTS admin_2fa_backup_codes (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    code_hash VARCHAR(255) NOT NULL,
    is_used TINYINT(1) DEFAULT 0,
    used_at TIMESTAMP NULL,
    used_ip VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_backup_admin_id (admin_id),
    INDEX idx_admin_2fa_backup_used (is_used),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for 2FA attempt rate limiting
CREATE TABLE IF NOT EXISTS admin_2fa_attempts (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_type ENUM('EMAIL_CODE', 'BACKUP_CODE') NOT NULL,
    is_successful TINYINT(1) DEFAULT 0,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_attempts_admin_id (admin_id),
    INDEX idx_admin_2fa_attempts_ip (ip_address),
    INDEX idx_admin_2fa_attempts_time (attempted_at),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table for 2FA audit logs
CREATE TABLE IF NOT EXISTS admin_2fa_logs (
    id VARCHAR(36) PRIMARY KEY,
    admin_id VARCHAR(36) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    metadata JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_2fa_logs_admin_id (admin_id),
    INDEX idx_admin_2fa_logs_action (action),
    INDEX idx_admin_2fa_logs_created_at (created_at),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add 2FA session tracking columns to sessions table
ALTER TABLE sessions 
ADD COLUMN IF NOT EXISTS requires_2fa TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_2fa_verified TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS pending_2fa_admin_id VARCHAR(36) NULL,
ADD INDEX IF NOT EXISTS idx_sessions_2fa_pending (pending_2fa_admin_id);
