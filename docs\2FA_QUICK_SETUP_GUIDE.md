# 2FA Quick Setup Guide
## Flix Salonce - Admin Two-Factor Authentication

### For System Administrators

#### Initial Setup (One-time)
1. **Database Migration**
   ```bash
   # Run this command in your terminal/command prompt
   mysql -u root -p flix_salonce < database/create_2fa_tables.sql
   ```

2. **Verify Installation**
   - Login to admin panel normally
   - Go to Admin Profile → Security Settings
   - You should see "Two-Factor Authentication" section

#### For Admin Users

#### Enabling 2FA
1. **Access Settings**
   - Login to admin panel
   - Click your profile name (top right)
   - Go to "Security Settings" section

2. **Enable 2FA**
   - Click "Enable 2FA" button
   - Keep both options checked:
     - ✅ Email Verification
     - ✅ Backup Codes
   - Click "Enable 2FA"

3. **Save Backup Codes**
   - System will generate 10 backup codes
   - **IMPORTANT**: Print or save these codes securely
   - Each code can only be used once
   - You'll need these if email is unavailable

#### Using 2FA

#### Normal Login Process
1. Enter email and password as usual
2. Click "Login"
3. **New Step**: Enter 6-digit code from email
4. Access granted to admin panel

#### If Email Code Doesn't Arrive
1. Check spam/junk folder
2. Click "Use Backup Code" on verification page
3. Enter one of your saved backup codes
4. Access granted to admin panel

#### Managing Backup Codes
- **Check Remaining**: View count in Security Settings
- **Generate New**: Click "Generate New Backup Codes"
- **Warning**: When you have 2 or fewer codes left, generate new ones

#### Disabling 2FA
1. Go to Admin Profile → Security Settings
2. Click "Disable 2FA"
3. Confirm the action
4. **Note**: This reduces account security

### Security Best Practices

#### For Backup Codes
- ✅ Print codes and store in secure location
- ✅ Don't share codes with anyone
- ✅ Generate new codes when running low
- ❌ Don't store codes in email or cloud storage
- ❌ Don't take screenshots of codes

#### For Email Security
- ✅ Use strong email password
- ✅ Enable email 2FA if available
- ✅ Check email regularly
- ❌ Don't use shared email accounts

### Troubleshooting

#### "Authentication Error" on Login
**Solution**: Contact system administrator to run database migration

#### Email Code Not Received
**Solutions**:
1. Check spam/junk folder
2. Wait 1-2 minutes for delivery
3. Use backup code instead
4. Contact system administrator

#### Backup Code Not Working
**Check**:
- Code entered exactly as shown (case-sensitive)
- Code hasn't been used before
- No extra spaces or characters

#### Locked Out (Too Many Attempts)
**Solution**: Wait 15 minutes, then try again

#### Lost All Backup Codes
**Solution**: Contact system administrator to disable 2FA temporarily

### Emergency Access

#### If You're Locked Out
1. **Wait**: If rate limited, wait 15 minutes
2. **Email**: Check all email folders for verification codes
3. **Backup Codes**: Use any remaining backup codes
4. **Admin Help**: Contact another admin or system administrator

#### For System Administrators
If an admin is locked out:
1. Access database directly
2. Temporarily disable 2FA:
   ```sql
   UPDATE admin_2fa_settings 
   SET is_enabled = 0 
   WHERE admin_id = 'ADMIN_USER_ID';
   ```
3. Admin can login normally and re-enable 2FA

### Support

#### Getting Help
- **Documentation**: See `docs/2FA_SYSTEM_DOCUMENTATION.md`
- **Logs**: Check PHP error logs for detailed error messages
- **Database**: Verify tables exist in `flix_salonce` database

#### Common Questions

**Q: Is 2FA mandatory?**
A: No, it's optional but highly recommended for security.

**Q: Can I use 2FA on mobile?**
A: Yes, email codes work on any device with email access.

**Q: How long do email codes last?**
A: 10 minutes from when they're sent.

**Q: How many backup codes do I get?**
A: 10 codes, each usable once.

**Q: Can I disable 2FA temporarily?**
A: Yes, but this reduces account security.

### Quick Reference

#### 2FA Status Indicators
- 🟢 **Enabled**: 2FA is active and protecting your account
- 🟡 **Low Backup Codes**: Generate new codes soon
- 🔴 **Disabled**: Account protected by password only

#### Important Numbers
- **Email Code Length**: 6 digits
- **Backup Code Length**: 8 characters
- **Code Expiry**: 10 minutes
- **Rate Limit**: 5 attempts per 15 minutes
- **Backup Codes Generated**: 10 codes

#### Key Files
- **Verification Page**: `/admin/auth/verify-2fa.php`
- **Backup Codes**: `/admin/auth/backup-codes.php`
- **Settings**: `/admin/profile/index.php` (Security Settings)
- **Documentation**: `/docs/2FA_SYSTEM_DOCUMENTATION.md`
