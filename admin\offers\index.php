<?php
require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create':
                    $data = [
                        'title' => $_POST['title'],
                        'description' => $_POST['description'],
                        'discount' => floatval($_POST['discount']),
                        'code' => strtoupper(trim($_POST['code'])),
                        'valid_from' => $_POST['valid_from'],
                        'valid_to' => $_POST['valid_to'],
                        'max_usage' => !empty($_POST['max_usage']) ? intval($_POST['max_usage']) : null,
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'image' => $_POST['image'] ?? null
                    ];
                    
                    createOffer($data);
                    $message = 'Offer created successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'update':
                    $id = $_POST['offer_id'];
                    $data = [
                        'title' => $_POST['title'],
                        'description' => $_POST['description'],
                        'discount' => floatval($_POST['discount']),
                        'code' => strtoupper(trim($_POST['code'])),
                        'valid_from' => $_POST['valid_from'],
                        'valid_to' => $_POST['valid_to'],
                        'max_usage' => !empty($_POST['max_usage']) ? intval($_POST['max_usage']) : null,
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'image' => $_POST['image'] ?? null
                    ];
                    
                    updateOffer($id, $data);
                    $message = 'Offer updated successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'delete':
                    $id = $_POST['offer_id'];
                    deleteOffer($id);
                    $message = 'Offer deleted successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'toggle_status':
                    $id = $_POST['offer_id'];
                    $offer = getOfferById($id);
                    updateOffer($id, ['is_active' => $offer['is_active'] ? 0 : 1]);
                    $message = 'Offer status updated successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get filters
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// Get offers
$offers = getAllOffers($search, $status, $limit, $offset);
$totalOffers = count(getAllOffers($search, $status));
$totalPages = ceil($totalOffers / $limit);

// Get statistics
$stats = getOfferStatistics();

$pageTitle = "Offers Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">Offers Management</h1>
                    <p class="mt-1 text-sm text-gray-300">Manage promotional offers and discount codes</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <button onclick="openModal()" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Add Offer
                    </button>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-tags text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Offers</p>
                        <p class="text-2xl font-semibold text-white"><?= $stats['total_offers'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Active Offers</p>
                        <p class="text-2xl font-semibold text-white"><?= $stats['active_offers'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Total Usage</p>
                        <p class="text-2xl font-semibold text-white"><?= $stats['total_usage'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Discount Given</p>
                        <p class="text-2xl font-semibold text-white"><?= formatCurrency($stats['total_discount_given']) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
            <form method="GET" class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                           placeholder="Search offers..."
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
                <div>
                    <select name="status" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                        <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                        <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                </div>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Filter
                </button>
                <?php if ($search || $status !== 'all'): ?>
                    <a href="<?= getBasePath() ?>/admin/offers" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        Clear
                    </a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Offers Table -->
        <div class="bg-secondary-800 rounded-lg overflow-hidden shadow">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-secondary-700">
                    <thead class="bg-secondary-700">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-1/4">Offer</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-32">Code</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-24">Discount</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-48">Valid Period</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-24">Usage</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-24">Status</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-64">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                        <?php if (empty($offers)): ?>
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center text-gray-400">
                                    <i class="fas fa-tags text-4xl mb-4"></i>
                                    <p class="text-lg">No offers found</p>
                                    <p class="text-sm">Create your first offer to get started</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($offers as $offer): ?>
                                <tr class="hover:bg-secondary-700 transition-colors">
                                    <td class="px-6 py-6">
                                        <div class="flex items-center">
                                            <?php if ($offer['image']): ?>
                                                <img class="h-10 w-10 rounded-lg object-cover mr-3" src="<?= htmlspecialchars($offer['image']) ?>" alt="">
                                            <?php else: ?>
                                                <div class="h-10 w-10 rounded-lg bg-salon-gold flex items-center justify-center mr-3">
                                                    <i class="fas fa-tag text-black"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="text-sm font-medium text-white"><?= htmlspecialchars($offer['title']) ?></div>
                                                <div class="text-sm text-gray-400"><?= htmlspecialchars(substr($offer['description'], 0, 50)) ?>...</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-6">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-salon-gold text-black">
                                            <?= htmlspecialchars($offer['code']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-6">
                                        <span class="text-xl font-semibold text-green-400"><?= $offer['discount'] ?>%</span>
                                    </td>
                                    <td class="px-6 py-6">
                                        <div class="text-sm text-white">
                                            <?= date('M j, Y', strtotime($offer['valid_from'])) ?>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            to <?= date('M j, Y', strtotime($offer['valid_to'])) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-6">
                                        <div class="text-sm text-white">
                                            <?= $offer['usage_count'] ?><?= $offer['max_usage'] ? '/' . $offer['max_usage'] : '' ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-6">
                                        <?php 
                                        $isExpired = strtotime($offer['valid_to']) < time();
                                        $statusClass = $offer['is_active'] && !$isExpired ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                                        $statusText = $offer['is_active'] && !$isExpired ? 'Active' : ($isExpired ? 'Expired' : 'Inactive');
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                            <?= $statusText ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-6">
                                        <div class="flex items-center space-x-3">
                                            <button onclick="editOffer('<?= $offer['id'] ?>')"
                                                    class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                                                <i class="fas fa-edit mr-2"></i>Edit
                                            </button>
                                            <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to <?= $offer['is_active'] ? 'deactivate' : 'activate' ?> this offer?')">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="offer_id" value="<?= $offer['id'] ?>">
                                                <button type="submit" class="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-yellow-700 transition-colors">
                                                    <i class="fas fa-<?= $offer['is_active'] ? 'eye-slash' : 'eye' ?> mr-2"></i><?= $offer['is_active'] ? 'Deactivate' : 'Activate' ?>
                                                </button>
                                            </form>
                                            <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this offer? This action cannot be undone.')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="offer_id" value="<?= $offer['id'] ?>">
                                                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                                    <i class="fas fa-trash mr-2"></i>Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="mt-6 flex items-center justify-between">
                <div class="text-sm text-gray-400">
                    Showing <?= $offset + 1 ?> to <?= min($offset + $limit, $totalOffers) ?> of <?= $totalOffers ?> results
                </div>
                <div class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                           class="px-3 py-2 bg-secondary-700 text-white rounded-lg hover:bg-secondary-600 transition-colors">
                            Previous
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                           class="px-3 py-2 <?= $i === $page ? 'bg-salon-gold text-black' : 'bg-secondary-700 text-white hover:bg-secondary-600' ?> rounded-lg transition-colors">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                           class="px-3 py-2 bg-secondary-700 text-white rounded-lg hover:bg-secondary-600 transition-colors">
                            Next
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Offer Modal -->
<div id="offerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="modalTitle" class="text-xl font-semibold text-white">Add New Offer</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="offerForm" method="POST" class="space-y-6">
                    <input type="hidden" id="action" name="action" value="create">
                    <input type="hidden" id="offer_id" name="offer_id" value="">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Offer Title *</label>
                            <input type="text" id="title" name="title" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                            <textarea id="description" name="description" rows="3" required
                                      class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                        </div>

                        <div>
                            <label for="discount" class="block text-sm font-medium text-gray-300 mb-2">Discount (%) *</label>
                            <input type="number" id="discount" name="discount" min="0" max="100" step="1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="code" class="block text-sm font-medium text-gray-300 mb-2">Promo Code *</label>
                            <input type="text" id="code" name="code" required style="text-transform: uppercase;"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="valid_from" class="block text-sm font-medium text-gray-300 mb-2">Valid From *</label>
                            <input type="date" id="valid_from" name="valid_from" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="valid_to" class="block text-sm font-medium text-gray-300 mb-2">Valid To *</label>
                            <input type="date" id="valid_to" name="valid_to" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="max_usage" class="block text-sm font-medium text-gray-300 mb-2">Max Usage (Optional)</label>
                            <input type="number" id="max_usage" name="max_usage" min="1"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-300 mb-2">Image URL (Optional)</label>
                            <input type="url" id="image" name="image"
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div class="md:col-span-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" checked
                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0">
                                <span class="ml-2 text-sm text-gray-300">Active</span>
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeModal()"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            <span id="submitText">Create Offer</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function openModal(offer = null) {
    const modal = document.getElementById('offerModal');
    const form = document.getElementById('offerForm');
    const modalTitle = document.getElementById('modalTitle');
    const submitText = document.getElementById('submitText');
    const action = document.getElementById('action');
    const offerId = document.getElementById('offer_id');

    if (offer) {
        // Edit mode
        modalTitle.textContent = 'Edit Offer';
        submitText.textContent = 'Update Offer';
        action.value = 'update';
        offerId.value = offer.id;

        // Fill form fields
        document.getElementById('title').value = offer.title;
        document.getElementById('description').value = offer.description;
        document.getElementById('discount').value = offer.discount;
        document.getElementById('code').value = offer.code;
        document.getElementById('valid_from').value = offer.valid_from.split(' ')[0];
        document.getElementById('valid_to').value = offer.valid_to.split(' ')[0];
        document.getElementById('max_usage').value = offer.max_usage || '';
        document.getElementById('image').value = offer.image || '';
        document.getElementById('is_active').checked = offer.is_active == 1;
    } else {
        // Create mode
        modalTitle.textContent = 'Add New Offer';
        submitText.textContent = 'Create Offer';
        action.value = 'create';
        offerId.value = '';
        form.reset();
        document.getElementById('is_active').checked = true;
    }

    modal.classList.remove('hidden');
}

function closeModal() {
    const modal = document.getElementById('offerModal');
    modal.classList.add('hidden');
}

function editOffer(offerId) {
    // Fetch offer data from server
    fetch(`<?= getBasePath() ?>/api/admin/offers/get.php?id=${offerId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(offer => {
            openModal(offer);
        })
        .catch(error => {
            console.error('Error fetching offer:', error);
            alert('Error loading offer data: ' + error.message);
        });
}

// Auto-generate promo code
document.getElementById('title').addEventListener('input', function() {
    const title = this.value;
    const codeField = document.getElementById('code');

    if (title && !codeField.value) {
        // Generate code from title
        const code = title.replace(/[^a-zA-Z0-9]/g, '').toUpperCase().substring(0, 10);
        codeField.value = code;
    }
});

// Close modal when clicking outside
document.getElementById('offerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Set minimum date to today
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('valid_from').min = today;
    document.getElementById('valid_to').min = today;
});

// Update valid_to minimum when valid_from changes
document.getElementById('valid_from').addEventListener('change', function() {
    document.getElementById('valid_to').min = this.value;
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
