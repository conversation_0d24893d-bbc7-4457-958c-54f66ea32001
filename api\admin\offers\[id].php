<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../../config/app.php';

// Check authentication and admin role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get offer ID from URL
$requestUri = $_SERVER['REQUEST_URI'];
$pathParts = explode('/', trim($requestUri, '/'));
$offerId = end($pathParts);

if (empty($offerId)) {
    http_response_code(400);
    echo json_encode(['error' => 'Offer ID is required']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetOffer($offerId);
            break;
            
        case 'PUT':
        case 'PATCH':
            handleUpdateOffer($offerId);
            break;
            
        case 'DELETE':
            handleDeleteOffer($offerId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetOffer($offerId) {
    $offer = getOfferById($offerId);
    
    if (!$offer) {
        http_response_code(404);
        echo json_encode(['error' => 'Offer not found']);
        return;
    }
    
    // Format dates for JSON
    $offer['valid_from'] = date('Y-m-d', strtotime($offer['valid_from']));
    $offer['valid_to'] = date('Y-m-d', strtotime($offer['valid_to']));
    $offer['created_at'] = date('Y-m-d H:i:s', strtotime($offer['created_at']));
    $offer['updated_at'] = date('Y-m-d H:i:s', strtotime($offer['updated_at']));
    
    // Add status information
    $offer['is_expired'] = strtotime($offer['valid_to']) < time();
    $offer['is_valid'] = $offer['is_active'] && !$offer['is_expired'];
    
    echo json_encode([
        'success' => true,
        'data' => $offer
    ]);
}

function handleUpdateOffer($offerId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    // Check if offer exists
    $existingOffer = getOfferById($offerId);
    if (!$existingOffer) {
        http_response_code(404);
        echo json_encode(['error' => 'Offer not found']);
        return;
    }
    
    // Prepare data for update (only include provided fields)
    $data = [];
    
    if (isset($input['title'])) {
        $data['title'] = trim($input['title']);
    }
    
    if (isset($input['description'])) {
        $data['description'] = trim($input['description']);
    }
    
    if (isset($input['discount'])) {
        $data['discount'] = floatval($input['discount']);
    }
    
    if (isset($input['code'])) {
        $data['code'] = strtoupper(trim($input['code']));
    }
    
    if (isset($input['validFrom'])) {
        $data['valid_from'] = $input['validFrom'];
    }
    
    if (isset($input['validTo'])) {
        $data['valid_to'] = $input['validTo'];
    }
    
    if (isset($input['maxUsage'])) {
        $data['max_usage'] = !empty($input['maxUsage']) ? intval($input['maxUsage']) : null;
    }
    
    if (isset($input['isActive'])) {
        $data['is_active'] = $input['isActive'] ? 1 : 0;
    }
    
    if (isset($input['image'])) {
        $data['image'] = !empty($input['image']) ? trim($input['image']) : null;
    }
    
    if (empty($data)) {
        http_response_code(400);
        echo json_encode(['error' => 'No fields to update']);
        return;
    }
    
    $offer = updateOffer($offerId, $data);
    
    // Format response
    $offer['valid_from'] = date('Y-m-d', strtotime($offer['valid_from']));
    $offer['valid_to'] = date('Y-m-d', strtotime($offer['valid_to']));
    $offer['created_at'] = date('Y-m-d H:i:s', strtotime($offer['created_at']));
    $offer['updated_at'] = date('Y-m-d H:i:s', strtotime($offer['updated_at']));
    $offer['is_expired'] = strtotime($offer['valid_to']) < time();
    $offer['is_valid'] = $offer['is_active'] && !$offer['is_expired'];
    
    echo json_encode([
        'success' => true,
        'data' => $offer,
        'message' => 'Offer updated successfully'
    ]);
}

function handleDeleteOffer($offerId) {
    // Check if offer exists
    $offer = getOfferById($offerId);
    if (!$offer) {
        http_response_code(404);
        echo json_encode(['error' => 'Offer not found']);
        return;
    }
    
    $result = deleteOffer($offerId);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Offer deleted successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete offer']);
    }
}
?>
