<?php
/**
 * Services Page
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get filter parameters
$searchQuery = sanitize($_GET['search'] ?? '');
$selectedCategory = sanitize($_GET['category'] ?? '');
$selectedSubcategory = sanitize($_GET['subcategory'] ?? '');
// Only allow price filters for admins
$minPrice = shouldShowPriceFilters() ? ($_GET['min_price'] ?? '') : '';
$maxPrice = shouldShowPriceFilters() ? ($_GET['max_price'] ?? '') : '';
$maxDuration = $_GET['max_duration'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$servicesPerPage = 12; // Number of services per page for "View More" functionality

// Find the selected category details for display
$selectedCategoryDetails = null;
$isRandomLoad = false; // This is now handled by the dropdown navigation
if ($selectedCategory) {
    foreach (getActiveServiceCategories() as $category) {
        if ($category['name'] === $selectedCategory) {
            $selectedCategoryDetails = $category;
            break;
        }
    }
}

// Get categories with subcategories for tabs interface
$categoriesWithSubs = getCategoriesWithSubcategories(true);

// Get all services grouped by category and subcategory
$servicesData = [];
foreach ($categoriesWithSubs as $category) {
    $categoryName = $category['name'];

    // Get services for this category using the category name (backward compatibility)
    $categoryServices = $database->fetchAll(
        "SELECT s.*, sc.name as subcategory_name
         FROM services s
         LEFT JOIN service_subcategories sc ON s.subcategory_id = sc.id
         WHERE s.category = ? AND s.is_active = 1
         ORDER BY s.subcategory_id, s.name",
        [$categoryName]
    );

    $servicesData[$categoryName] = [
        'category' => $category,
        'subcategories' => [],
        'services_without_subcategory' => []
    ];

    // Group services by subcategory
    foreach ($categoryServices as $service) {
        if ($service['subcategory_id']) {
            $subcategoryName = $service['subcategory_name'];
            if (!isset($servicesData[$categoryName]['subcategories'][$subcategoryName])) {
                $servicesData[$categoryName]['subcategories'][$subcategoryName] = [];
            }
            $servicesData[$categoryName]['subcategories'][$subcategoryName][] = $service;
        } else {
            $servicesData[$categoryName]['services_without_subcategory'][] = $service;
        }
    }
}

// Keep the old categories for backward compatibility with filters
$activeCategories = getActiveServiceCategories();

// Build query for filtered services
$whereClause = "WHERE s.is_active = 1";
$params = [];
$joins = "";

if ($searchQuery) {
    $whereClause .= " AND (s.name LIKE ? OR s.description LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
}

if ($selectedCategory) {
    $whereClause .= " AND s.category = ?";
    $params[] = $selectedCategory;
}

if ($selectedSubcategory) {
    // Join with subcategories table to filter by subcategory name
    $joins = "LEFT JOIN service_subcategories sc ON s.subcategory_id = sc.id";
    $whereClause .= " AND sc.name = ?";
    $params[] = $selectedSubcategory;
}

if ($minPrice !== '') {
    $whereClause .= " AND s.price >= ?";
    $params[] = floatval($minPrice);
}

if ($maxPrice !== '') {
    $whereClause .= " AND s.price <= ?";
    $params[] = floatval($maxPrice);
}

if ($maxDuration !== '') {
    $whereClause .= " AND s.duration <= ?";
    $params[] = intval($maxDuration);
}

// Get total count for pagination
$countQuery = "SELECT COUNT(DISTINCT s.id) as total FROM services s $joins $whereClause";
$totalServices = $database->fetch($countQuery, $params)['total'];

// Calculate pagination
$totalPages = ceil($totalServices / $servicesPerPage);
$offset = ($page - 1) * $servicesPerPage;

// Get filtered services with pagination
$serviceQuery = "SELECT DISTINCT s.*, sc.name as subcategory_name
                 FROM services s
                 LEFT JOIN service_subcategories sc ON s.subcategory_id = sc.id
                 $whereClause
                 ORDER BY s.category, s.name
                 LIMIT $servicesPerPage OFFSET $offset";

$allServices = $database->fetchAll($serviceQuery, $params);

// Group services by category using the dynamic categories
$servicesByCategory = [];
$categoryDescriptions = [];

// Check if any filters are applied
$hasFilters = $searchQuery || $selectedCategory || $selectedSubcategory || $minPrice !== '' || $maxPrice !== '' || $maxDuration !== '';

// First, populate category descriptions from the service_categories table
foreach ($activeCategories as $category) {
    $categoryDescriptions[$category['name']] = $category['description'];
    $servicesByCategory[$category['name']] = [];
}

// Then group services under their categories
foreach ($allServices as $service) {
    $categoryName = $service['category'];
    if ($categoryName && isset($servicesByCategory[$categoryName])) {
        $servicesByCategory[$categoryName][] = $service;
    }
}

// Always remove empty categories when filters are applied
// This ensures only relevant categories with matching services are shown
if ($hasFilters) {
    $servicesByCategory = array_filter($servicesByCategory, function($services) {
        return !empty($services);
    });

    // If we have filtered results, sort categories by the number of services (most relevant first)
    if (!empty($servicesByCategory)) {
        uksort($servicesByCategory, function($a, $b) use ($servicesByCategory) {
            $countA = count($servicesByCategory[$a]);
            $countB = count($servicesByCategory[$b]);

            // Sort by count descending, then by name ascending
            if ($countA === $countB) {
                return strcmp($a, $b);
            }
            return $countB - $countA;
        });
    }
} else {
    // When no filters are applied, remove empty categories to keep the page clean
    $servicesByCategory = array_filter($servicesByCategory, function($services) {
        return !empty($services);
    });
}

// Helper function to build pagination URLs
function buildPaginationUrl($pageNum, $search = '', $category = '', $subcategory = '', $minPrice = '', $maxPrice = '', $maxDuration = '', $showAll = false) {
    $params = ['page' => $pageNum];
    if ($search) $params['search'] = $search;
    if ($category) $params['category'] = $category;
    if ($subcategory) $params['subcategory'] = $subcategory;
    if ($minPrice !== '') $params['min_price'] = $minPrice;
    if ($maxPrice !== '') $params['max_price'] = $maxPrice;
    if ($maxDuration !== '') $params['max_duration'] = $maxDuration;
    if ($showAll) $params['show_all'] = '1';

    return getBasePath() . '/services.php?' . http_build_query($params);
}

$pageTitle = $selectedCategory ? $selectedCategory . " Services" : "Our Services";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Line clamp utilities for text truncation */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced hover effects */
.group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

.group:hover .group-hover\:text-salon-gold {
    color: #D4AF37;
}

/* Smooth transitions for all interactive elements */
.transition-all {
    transition: all 0.3s ease;
}
</style>

<!-- Hero Section -->
<section class="relative bg-salon-black py-24">
    <div class="absolute inset-0 bg-gradient-to-r from-salon-black via-salon-black/90 to-transparent"></div>
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30" 
         style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Our Premium <span class="text-salon-gold">Services</span>
            </h1>
            <p class="text-xl text-gray-300 mb-8">
                Discover our comprehensive range of beauty and wellness services, designed to make you look and feel your absolute best.
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
                <a href="<?= getBasePath() ?>/booking" 
                   class="inline-flex items-center justify-center px-8 py-4 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-all hover:scale-105">
                    Book Appointment
                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
                <a href="#services" 
                   class="inline-flex items-center justify-center px-8 py-4 border border-salon-gold text-salon-gold font-semibold rounded-lg hover:bg-salon-gold hover:text-black transition-all">
                    View Services
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Category Filter Indicator -->
<?php if ($selectedCategoryDetails): ?>
<section class="py-6 bg-salon-black border-b border-secondary-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between bg-secondary-900 rounded-lg p-4">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-salon-gold rounded-full mr-3"></div>
                <div>
                    <h3 class="text-lg font-semibold text-white">
                        Showing services in: <span class="text-salon-gold"><?= htmlspecialchars($selectedCategoryDetails['name']) ?></span>
                    </h3>
                    <?php if ($selectedCategoryDetails['description']): ?>
                        <p class="text-sm text-gray-400 mt-1"><?= htmlspecialchars($selectedCategoryDetails['description']) ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-400">
                    <?= count($allServices) ?> service<?= count($allServices) !== 1 ? 's' : '' ?> found
                </span>
                <a href="<?= getBasePath() ?>/services"
                   class="inline-flex items-center px-3 py-1 bg-salon-gold text-black text-sm font-medium rounded-lg hover:bg-gold-light transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Clear Filter
                </a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Filter and Search Section -->
<section class="py-12 bg-secondary-900 border-b border-secondary-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-salon-black rounded-lg p-6 shadow-lg border border-secondary-700">
            <form method="GET" action="<?= getBasePath() ?>/services.php" class="space-y-6">
                <!-- Search, Category and Subcategory Row -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Search Input -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-300 mb-2">
                            Search Services
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="<?= htmlspecialchars($searchQuery) ?>"
                                   placeholder="Search by service name or description..."
                                   class="w-full pl-10 pr-4 py-3 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-300 mb-2">
                            Category
                        </label>
                        <select id="category"
                                name="category"
                                onchange="loadSubcategoriesFilter()"
                                class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                            <option value="">All Categories</option>
                            <?php foreach ($activeCategories as $category): ?>
                                <option value="<?= htmlspecialchars($category['name']) ?>" data-id="<?= htmlspecialchars($category['id']) ?>"
                                        <?= $selectedCategory === $category['name'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Subcategory Filter -->
                    <div>
                        <label for="subcategory" class="block text-sm font-medium text-gray-300 mb-2">
                            Subcategory
                        </label>
                        <select id="subcategory"
                                name="subcategory"
                                class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                            <option value="">All Subcategories</option>
                            <?php if ($selectedSubcategory): ?>
                                <option value="<?= htmlspecialchars($selectedSubcategory) ?>" selected>
                                    <?= htmlspecialchars($selectedSubcategory) ?>
                                </option>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>

                <!-- Price and Duration Filters -->
                <div class="grid grid-cols-1 <?= shouldShowPriceFilters() ? 'md:grid-cols-3' : 'md:grid-cols-1' ?> gap-4">
                    <?php if (shouldShowPriceFilters()): ?>
                        <!-- Min Price -->
                        <div>
                            <label for="min_price" class="block text-sm font-medium text-gray-300 mb-2">
                                Min Price (TSH)
                            </label>
                            <input type="number"
                                   id="min_price"
                                   name="min_price"
                                   value="<?= htmlspecialchars($minPrice) ?>"
                                   min="0"
                                   step="1"
                                   placeholder="0"
                                   class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                        </div>

                        <!-- Max Price -->
                        <div>
                            <label for="max_price" class="block text-sm font-medium text-gray-300 mb-2">
                                Max Price (TSH)
                            </label>
                            <input type="number"
                                   id="max_price"
                                   name="max_price"
                                   value="<?= htmlspecialchars($maxPrice) ?>"
                                   min="0"
                                   step="1"
                                   placeholder="999"
                                   class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                        </div>
                    <?php endif; ?>

                    <!-- Max Duration -->
                    <div>
                        <label for="max_duration" class="block text-sm font-medium text-gray-300 mb-2">
                            Max Duration (minutes)
                        </label>
                        <input type="number"
                               id="max_duration"
                               name="max_duration"
                               value="<?= htmlspecialchars($maxDuration) ?>"
                               min="1"
                               placeholder="180"
                               class="w-full py-3 px-4 bg-secondary-900 border border-secondary-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button type="submit"
                            class="inline-flex items-center justify-center px-6 py-3 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search Services
                    </button>

                    <?php if ($searchQuery || $selectedCategory || $selectedSubcategory || $minPrice || $maxPrice || $maxDuration): ?>
                        <a href="<?= getBasePath() ?>/services.php"
                           class="inline-flex items-center justify-center px-6 py-3 border border-gray-600 text-gray-300 font-semibold rounded-lg hover:bg-gray-600 hover:text-white transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Clear Filters
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Results Summary -->
                <?php if ($hasFilters): ?>
                    <div class="text-center pt-4 border-t border-secondary-700">
                        <p class="text-gray-300">
                            <?php
                            $displayedServices = count($allServices);
                            $totalCategories = count($servicesByCategory);
                            if ($totalServices > 0): ?>
                                <?php if ($showAll || $totalServices <= $servicesPerPage): ?>
                                    <!-- Showing all services -->
                                    Showing all <span class="font-semibold text-salon-gold"><?= $totalServices ?></span> service<?= $totalServices !== 1 ? 's' : '' ?>
                                <?php else: ?>
                                    <!-- Showing paginated results -->
                                    Showing <span class="font-semibold text-salon-gold"><?= $displayedServices ?></span> of <span class="font-semibold text-salon-gold"><?= $totalServices ?></span> service<?= $totalServices !== 1 ? 's' : '' ?>
                                    <span class="text-sm text-gray-400">(Page <?= $page ?> of <?= $totalPages ?>)</span>
                                <?php endif; ?>
                                <?php if ($totalCategories > 0): ?>
                                    in <span class="font-semibold text-salon-gold"><?= $totalCategories ?></span> categor<?= $totalCategories !== 1 ? 'ies' : 'y' ?>
                                <?php endif; ?>
                                <?php if ($searchQuery): ?>
                                    matching "<span class="font-semibold text-white"><?= htmlspecialchars($searchQuery) ?></span>"
                                <?php endif; ?>
                                <?php if ($selectedCategory): ?>
                                    in <span class="font-semibold text-white"><?= htmlspecialchars($selectedCategory) ?></span>
                                <?php endif; ?>
                                <?php if ($selectedSubcategory): ?>
                                    > <span class="font-semibold text-salon-gold"><?= htmlspecialchars($selectedSubcategory) ?></span>
                                <?php endif; ?>
                                <?php if (shouldShowPricing() && ($minPrice !== '' || $maxPrice !== '')): ?>
                                    <?php if ($minPrice !== '' && $maxPrice !== ''): ?>
                                        with price between <span class="font-semibold text-white"><?= formatCurrency($minPrice, null, true) ?></span> - <span class="font-semibold text-white"><?= formatCurrency($maxPrice, null, true) ?></span>
                                    <?php elseif ($minPrice !== ''): ?>
                                        with price from <span class="font-semibold text-white"><?= formatCurrency($minPrice, null, true) ?></span>
                                    <?php else: ?>
                                        with price up to <span class="font-semibold text-white"><?= formatCurrency($maxPrice, null, true) ?></span>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if ($maxDuration !== ''): ?>
                                    with duration up to <span class="font-semibold text-white"><?= $maxDuration ?></span> minutes
                                <?php endif; ?>

                                <?php if (!$showAll && $totalServices > $servicesPerPage): ?>
                                    <br><span class="text-sm text-salon-gold">💡 Use pagination below or add more filters to narrow results</span>
                                    <br><br>
                                    <a href="<?= buildPaginationUrl(1, $searchQuery, $selectedCategory, $selectedSubcategory, $minPrice, $maxPrice, $maxDuration, true) ?>"
                                       class="inline-flex items-center px-4 py-2 bg-salon-gold text-black rounded-lg hover:bg-gold-light transition-colors font-semibold text-sm">
                                        <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Show All <?= $totalServices ?> Services
                                    </a>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="text-gray-400">No services found matching your criteria</span>
                                <br>
                                <span class="text-sm text-gray-500 mt-2 block">Try adjusting your filters or search terms</span>
                            <?php endif; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
</section>

<!-- Active Filters Indicator -->
<?php if ($hasFilters && !empty($servicesByCategory)): ?>
<section class="py-4 bg-secondary-900 border-b border-secondary-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex flex-wrap items-center gap-2">
                <span class="text-sm text-gray-400">Active filters:</span>
                <?php if ($searchQuery): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-salon-gold/20 text-salon-gold border border-salon-gold/30">
                        Search: "<?= htmlspecialchars($searchQuery) ?>"
                    </span>
                <?php endif; ?>
                <?php if ($selectedCategory): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30">
                        Category: <?= htmlspecialchars($selectedCategory) ?>
                    </span>
                <?php endif; ?>
                <?php if ($selectedSubcategory): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400 border border-purple-500/30">
                        Subcategory: <?= htmlspecialchars($selectedSubcategory) ?>
                    </span>
                <?php endif; ?>
                <?php if (shouldShowPricing() && $minPrice !== ''): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
                        Min: <?= formatCurrency($minPrice, null, true) ?>
                    </span>
                <?php endif; ?>
                <?php if (shouldShowPricing() && $maxPrice !== ''): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
                        Max: <?= formatCurrency($maxPrice, null, true) ?>
                    </span>
                <?php endif; ?>
                <?php if ($maxDuration !== ''): ?>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400 border border-purple-500/30">
                        Duration: ≤<?= $maxDuration ?>min
                    </span>
                <?php endif; ?>
            </div>
            <a href="<?= getBasePath() ?>/services.php"
               class="inline-flex items-center text-sm text-gray-400 hover:text-salon-gold transition-colors">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Clear all
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Services Section with Tabs -->
<section id="services" class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                Our Premium <span class="text-salon-gold">Services</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Explore our comprehensive range of beauty and wellness services organized by category
            </p>
        </div>

        <?php if ($hasFilters): ?>
            <!-- Show filtered results using old system -->
            <?php if (empty($servicesByCategory)): ?>
                <!-- No Services Found -->
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-white mb-4">No Services Found</h2>
                    <p class="text-gray-300 mb-6">No services match your current search criteria.</p>
                    <div class="space-y-4 mb-8">
                        <p class="text-gray-400 text-sm">Try:</p>
                        <ul class="text-gray-400 text-sm space-y-2 max-w-md mx-auto">
                            <?php if (shouldShowPricing()): ?>
                                <li>• Adjusting your price range</li>
                            <?php endif; ?>
                            <li>• Removing some filters</li>
                            <li>• Using different search terms</li>
                            <li>• Selecting a different category</li>
                        </ul>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?= getBasePath() ?>/services.php"
                           class="inline-flex items-center px-6 py-3 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Clear All Filters
                        </a>
                        <a href="<?= getBasePath() ?>/contact"
                           class="inline-flex items-center px-6 py-3 border border-salon-gold text-salon-gold font-semibold rounded-lg hover:bg-salon-gold hover:text-black transition-colors">
                            Contact Us for Help
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <!-- Filtered Results -->
                <?php foreach ($servicesByCategory as $category => $categoryServices): ?>
                    <div class="mb-20 last:mb-0">
                        <!-- Category Header -->
                        <div class="text-center mb-12">
                            <h2 class="text-3xl md:text-4xl font-bold text-white mb-3">
                                <?= htmlspecialchars($category) ?>
                            </h2>
                            <?php if (isset($categoryDescriptions[$category]) && $categoryDescriptions[$category]): ?>
                                <p class="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
                                    <?= htmlspecialchars($categoryDescriptions[$category]) ?>
                                </p>
                            <?php endif; ?>
                            <div class="w-24 h-1 bg-salon-gold mx-auto"></div>
                        </div>

                        <!-- Services Grid -->
                        <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                            <?php foreach ($categoryServices as $service): ?>
                                <?php include __DIR__ . '/includes/service_card.php'; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- Pagination for Filtered Results -->
                <?php if ($totalPages > 1): ?>
                    <div class="mt-12">
                        <nav class="flex items-center justify-between border-t border-secondary-700 pt-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="<?= buildPaginationUrl($page - 1, $searchQuery, $selectedCategory, $selectedSubcategory, $minPrice, $maxPrice, $maxDuration) ?>"
                                       class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-800 hover:bg-secondary-700">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                <?php if ($page < $totalPages): ?>
                                    <a href="<?= buildPaginationUrl($page + 1, $searchQuery, $selectedCategory, $selectedSubcategory, $minPrice, $maxPrice, $maxDuration) ?>"
                                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-800 hover:bg-secondary-700">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-300">
                                        Showing <span class="font-medium"><?= ($page - 1) * $servicesPerPage + 1 ?></span> to
                                        <span class="font-medium"><?= min($page * $servicesPerPage, $totalServices) ?></span> of
                                        <span class="font-medium"><?= $totalServices ?></span> results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        <?php
                                        $startPage = max(1, $page - 2);
                                        $endPage = min($totalPages, $page + 2);

                                        if ($page > 1): ?>
                                            <a href="<?= buildPaginationUrl(1, $searchQuery, $selectedCategory, $selectedSubcategory, $minPrice, $maxPrice, $maxDuration) ?>"
                                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-300 hover:bg-secondary-700">
                                                <span class="sr-only">First</span>
                                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                        <?php endif; ?>

                                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                            <a href="<?= buildPaginationUrl($i, $searchQuery, $selectedCategory, $selectedSubcategory, $minPrice, $maxPrice, $maxDuration) ?>"
                                               class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'border-secondary-600 bg-secondary-800 text-gray-300 hover:bg-secondary-700' ?>">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>

                                        <?php if ($page < $totalPages): ?>
                                            <a href="<?= buildPaginationUrl($totalPages, $searchQuery, $selectedCategory, $selectedSubcategory, $minPrice, $maxPrice, $maxDuration) ?>"
                                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-300 hover:bg-secondary-700">
                                                <span class="sr-only">Last</span>
                                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                            </div>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php elseif (empty($servicesData)): ?>
            <!-- No Services Available -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2z"></path>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-white mb-4">No Services Available</h2>
                <p class="text-gray-300 mb-8">We're currently updating our services. Please check back soon!</p>
                <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center px-6 py-3 bg-salon-gold text-black font-semibold rounded-lg hover:bg-gold-light transition-colors">
                    Contact Us for Information
                </a>
            </div>
        <?php else: ?>
            <!-- Display each main category as a section -->
            <?php foreach ($servicesData as $categoryName => $categoryData): ?>
                <div class="mb-16 last:mb-0">
                    <!-- Main Category Header -->
                    <div class="text-center mb-8">
                        <h2 class="text-3xl md:text-4xl font-bold text-white mb-3">
                            <?= htmlspecialchars($categoryName) ?>
                        </h2>
                        <?php if ($categoryData['category']['description']): ?>
                            <p class="text-lg text-gray-300 mb-6 max-w-2xl mx-auto">
                                <?= htmlspecialchars($categoryData['category']['description']) ?>
                            </p>
                        <?php endif; ?>
                        <div class="w-24 h-1 bg-salon-gold mx-auto"></div>
                    </div>

                    <!-- Subcategory Tabs (if any subcategories exist) -->
                    <?php if (!empty($categoryData['subcategories'])): ?>
                        <div class="mb-8">
                            <div class="border-b border-secondary-700">
                                <nav class="-mb-px flex flex-wrap justify-center gap-4">
                                    <!-- All Services Tab -->
                                    <button onclick="showSubcategory('<?= htmlspecialchars($categoryName) ?>', 'all')"
                                            class="subcategory-tab whitespace-nowrap py-3 px-4 border-b-2 border-salon-gold text-salon-gold font-medium text-sm transition-colors"
                                            data-category="<?= htmlspecialchars($categoryName) ?>"
                                            data-subcategory="all">
                                        All <?= htmlspecialchars($categoryName) ?>
                                        <span class="ml-2 bg-salon-gold/20 text-salon-gold py-1 px-2 rounded-full text-xs">
                                            <?php
                                            $totalServices = count($categoryData['services_without_subcategory']);
                                            foreach ($categoryData['subcategories'] as $subServices) {
                                                $totalServices += count($subServices);
                                            }
                                            echo $totalServices;
                                            ?>
                                        </span>
                                    </button>

                                    <!-- Individual Subcategory Tabs -->
                                    <?php foreach ($categoryData['subcategories'] as $subcategoryName => $subServices): ?>
                                        <button onclick="showSubcategory('<?= htmlspecialchars($categoryName) ?>', '<?= htmlspecialchars($subcategoryName) ?>')"
                                                class="subcategory-tab whitespace-nowrap py-3 px-4 border-b-2 border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300 font-medium text-sm transition-colors"
                                                data-category="<?= htmlspecialchars($categoryName) ?>"
                                                data-subcategory="<?= htmlspecialchars($subcategoryName) ?>">
                                            <?= htmlspecialchars($subcategoryName) ?>
                                            <span class="ml-2 bg-secondary-700 text-gray-300 py-1 px-2 rounded-full text-xs">
                                                <?= count($subServices) ?>
                                            </span>
                                        </button>
                                    <?php endforeach; ?>
                                </nav>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Services Display -->
                    <div id="services-<?= htmlspecialchars($categoryName) ?>">
                        <!-- All Services View (Default) -->
                        <div id="subcategory-<?= htmlspecialchars($categoryName) ?>-all" class="subcategory-content">
                            <?php
                            // Collect all services for this category
                            $allCategoryServices = $categoryData['services_without_subcategory'];
                            foreach ($categoryData['subcategories'] as $subServices) {
                                $allCategoryServices = array_merge($allCategoryServices, $subServices);
                            }

                            // Show first 4 services
                            $displayServices = array_slice($allCategoryServices, 0, 4);
                            $remainingServices = array_slice($allCategoryServices, 4);
                            $remainingCount = count($remainingServices);
                            ?>

                            <?php if (!empty($displayServices)): ?>
                                <!-- Initially displayed services -->
                                <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                                    <?php foreach ($displayServices as $service): ?>
                                        <?php include __DIR__ . '/includes/service_card.php'; ?>
                                    <?php endforeach; ?>
                                </div>

                                <!-- View More Button for Category Filter -->
                                <?php if ($remainingCount > 0): ?>
                                    <div class="text-center">
                                        <button onclick="viewAllServices('<?= htmlspecialchars($categoryName) ?>', 'all')"
                                                class="inline-flex items-center px-6 py-3 bg-salon-gold text-black rounded-lg hover:bg-gold-light transition-colors font-semibold">
                                            <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View All <?= count($allCategoryServices) ?> <?= htmlspecialchars($categoryName) ?> Services
                                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </button>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <p class="text-gray-400">No services available in this category yet.</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Individual Subcategory Views -->
                        <?php foreach ($categoryData['subcategories'] as $subcategoryName => $subServices): ?>
                            <div id="subcategory-<?= htmlspecialchars($categoryName) ?>-<?= htmlspecialchars($subcategoryName) ?>"
                                 class="subcategory-content hidden">
                                <?php
                                $displayServices = array_slice($subServices, 0, 4);
                                $remainingServices = array_slice($subServices, 4);
                                $remainingCount = count($remainingServices);
                                ?>

                                <?php if (!empty($displayServices)): ?>
                                    <!-- Initially displayed services -->
                                    <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                                        <?php foreach ($displayServices as $service): ?>
                                            <?php include __DIR__ . '/includes/service_card.php'; ?>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- View More Button for Subcategory Filter -->
                                    <?php if ($remainingCount > 0): ?>
                                        <div class="text-center">
                                            <button onclick="viewAllServices('<?= htmlspecialchars($categoryName) ?>', '<?= htmlspecialchars($subcategoryName) ?>')"
                                                    class="inline-flex items-center px-6 py-3 bg-salon-gold text-black rounded-lg hover:bg-gold-light transition-colors font-semibold">
                                                <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                View All <?= count($subServices) ?> <?= htmlspecialchars($subcategoryName) ?> Services
                                                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="text-center py-8">
                                        <p class="text-gray-400">No services available in this subcategory yet.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                Why Choose <span class="text-salon-gold">Flix Salon & SPA</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Experience the difference with our premium services and expert professionals
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Expert Professionals</h3>
                <p class="text-gray-300">Certified and experienced stylists and therapists</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Premium Products</h3>
                <p class="text-gray-300">High-quality, professional-grade products only</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Flexible Scheduling</h3>
                <p class="text-gray-300">Easy online booking with flexible time slots</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Loyalty Rewards</h3>
                <p class="text-gray-300">Earn points with every visit and get exclusive benefits</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-salon-gold to-amber-400">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-black mb-4">
            Ready to Transform Your Look?
        </h2>
        <p class="text-xl text-black/80 mb-8 max-w-2xl mx-auto">
            Book your appointment today and experience the luxury of Flix Salon & SPA
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?= getBasePath() ?>/booking" 
               class="inline-flex items-center justify-center px-8 py-4 bg-black text-salon-gold font-semibold rounded-lg hover:bg-secondary-800 transition-all hover:scale-105">
                Book Appointment Now
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
            <a href="<?= getBasePath() ?>/contact" 
               class="inline-flex items-center justify-center px-8 py-4 border-2 border-black text-black font-semibold rounded-lg hover:bg-black hover:text-salon-gold transition-all">
                Contact Us
            </a>
        </div>
    </div>
</section>

<!-- Service Details Modal -->
<div id="serviceModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 id="modalServiceName" class="text-2xl font-bold text-white"></h2>
                <button onclick="closeServiceModal()" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div id="modalServiceContent">
                <!-- Service details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Service Variations Selection Modal -->
<div id="variationsSelectionModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-900/95 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 w-full max-w-2xl mx-4 max-h-screen overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="variationsSelectionTitle" class="text-2xl font-bold text-white">Choose Service Option</h2>
            <button onclick="closeVariationsSelectionModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div id="variationsSelectionContent" class="space-y-4">
            <!-- Variations will be loaded here -->
        </div>
    </div>
</div>

<script>
// CSS escape fallback for older browsers
function cssEscape(value) {
    if (typeof CSS !== 'undefined' && CSS.escape) {
        return CSS.escape(value);
    }
    // Fallback for older browsers
    return value.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g, '\\$&');
}

// Subcategory tabs functionality
function showSubcategory(categoryName, subcategoryName) {
    console.log('showSubcategory called:', categoryName, subcategoryName);

    // Hide all subcategory contents for this category
    const servicesContainer = document.getElementById(`services-${categoryName}`);
    if (servicesContainer) {
        servicesContainer.querySelectorAll('.subcategory-content').forEach(content => {
            content.classList.add('hidden');
        });
    } else {
        console.warn('Services container not found for category:', categoryName);
    }

    // Show selected subcategory content
    const selectedContent = document.getElementById(`subcategory-${categoryName}-${subcategoryName}`);
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
        console.log('Showing content for:', categoryName, subcategoryName);
    } else {
        console.warn('Selected content not found for:', categoryName, subcategoryName);
    }

    // Update subcategory tab styles for this category only
    const categoryTabs = document.querySelectorAll(`[data-category="${categoryName}"].subcategory-tab`);
    console.log('Found tabs for category:', categoryName, categoryTabs.length);

    categoryTabs.forEach(tab => {
        tab.classList.remove('border-salon-gold', 'text-salon-gold');
        tab.classList.add('border-transparent', 'text-gray-400');
    });

    // Highlight selected subcategory tab
    const selectedTab = document.querySelector(`[data-category="${categoryName}"][data-subcategory="${subcategoryName}"]`);
    if (selectedTab) {
        selectedTab.classList.remove('border-transparent', 'text-gray-400');
        selectedTab.classList.add('border-salon-gold', 'text-salon-gold');
        console.log('Highlighted tab for:', categoryName, subcategoryName);
    } else {
        console.warn('Selected tab not found for:', categoryName, subcategoryName);
    }
}

function viewAllServices(categoryName, subcategoryName) {
    // Create URL with filters to show ALL services for specific category/subcategory
    let url = `<?= getBasePath() ?>/services.php?category=${encodeURIComponent(categoryName)}`;

    // If it's a specific subcategory (not 'all'), add subcategory filter
    if (subcategoryName !== 'all') {
        url += `&subcategory=${encodeURIComponent(subcategoryName)}`;
    }

    // Redirect to filtered view showing only services from this category/subcategory
    window.location.href = url;
}

// Load subcategories for filter dropdown
function loadSubcategoriesFilter() {
    const categorySelect = document.getElementById('category');
    const subcategorySelect = document.getElementById('subcategory');
    const selectedOption = categorySelect.options[categorySelect.selectedIndex];

    // Clear subcategory options
    subcategorySelect.innerHTML = '<option value="">All Subcategories</option>';

    if (selectedOption.value && selectedOption.dataset.id) {
        const categoryId = selectedOption.dataset.id;

        // Fetch subcategories for the selected category
        fetch(`<?= getBasePath() ?>/api/subcategories.php?category_id=${categoryId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.subcategories) {
                    data.subcategories.forEach(subcategory => {
                        const option = document.createElement('option');
                        option.value = subcategory.name;
                        option.textContent = subcategory.name;
                        subcategorySelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading subcategories:', error);
            });
    }
}

// Check if service has variations before booking
function checkServiceVariations(serviceId, serviceName, price, duration) {
    // First check if service has variations
    fetch(`<?= getBasePath() ?>/api/customer/service_variations.php?service_id=${serviceId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.has_variations && data.variations.length > 0) {
                // Show variations selection modal
                showVariationsSelectionModal(data.service, data.variations);
            } else {
                // No variations, proceed with direct booking
                <?php if (shouldShowPricing()): ?>
                    bookService(serviceId, serviceName, price, duration);
                <?php else: ?>
                    bookService(serviceId, serviceName, 0, duration); // Hide price from booking
                <?php endif; ?>
            }
        })
        .catch(error => {
            console.error('Error checking variations:', error);
            // Fallback to direct booking
            <?php if (shouldShowPricing()): ?>
                bookService(serviceId, serviceName, price, duration);
            <?php else: ?>
                bookService(serviceId, serviceName, 0, duration); // Hide price from booking
            <?php endif; ?>
        });
}

function bookService(serviceId, serviceName, price, duration, variationId = null) {
    // Check if user is logged in
    <?php if (isLoggedIn()): ?>
        // Redirect to booking page with service pre-selected
        let url = `<?= getBasePath() ?>/customer/book/?service=${serviceId}`;
        if (variationId) {
            url += `&variation=${variationId}`;
        }
        window.location.href = url;
    <?php else: ?>
        // Store service selection in sessionStorage for persistence across authentication
        const serviceSelection = {
            serviceId: serviceId,
            serviceName: serviceName,
            price: price,
            duration: duration,
            variationId: variationId,
            timestamp: Date.now()
        };
        sessionStorage.setItem('pendingServiceBooking', JSON.stringify(serviceSelection));

        // Redirect to login page with return URL
        let redirectUrl = `/customer/book/?service=${serviceId}`;
        if (variationId) {
            redirectUrl += `&variation=${variationId}`;
        }

        if (confirm('Please log in to book an appointment. Would you like to log in now?')) {
            window.location.href = `<?= getBasePath() ?>/auth/login.php?redirect=${encodeURIComponent(redirectUrl)}`;
        }
    <?php endif; ?>
}

function showVariationsSelectionModal(service, variations) {
    document.getElementById('variationsSelectionTitle').textContent = `Choose ${service.name} Option`;

    const content = document.getElementById('variationsSelectionContent');
    content.innerHTML = '';

    // Add service description if available
    if (service.description) {
        const description = document.createElement('div');
        description.className = 'mb-6 p-4 bg-secondary-800/50 rounded-lg border border-secondary-700';
        description.innerHTML = `
            <p class="text-gray-300">${escapeHtml(service.description)}</p>
        `;
        content.appendChild(description);
    }

    // Add variations
    variations.forEach(variation => {
        const variationDiv = document.createElement('div');
        variationDiv.className = 'border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300';
        variationDiv.onclick = () => {
            closeVariationsSelectionModal();
            bookService(service.id, service.name, variation.price, variation.duration, variation.id);
        };

        variationDiv.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <h4 class="text-lg font-semibold text-white">${escapeHtml(variation.name)}</h4>
                <?php if (shouldShowPricing()): ?>
                <span class="text-2xl font-bold text-salon-gold"><?= CURRENCY_SYMBOL ?> ${parseFloat(variation.price).toLocaleString()}</span>
                <?php else: ?>
                <span class="text-2xl font-bold text-salon-gold">Contact for pricing</span>
                <?php endif; ?>
            </div>
            ${variation.description ? `<p class="text-sm text-gray-400 mb-4">${escapeHtml(variation.description)}</p>` : ''}
            <div class="flex items-center justify-between text-sm text-gray-500">
                <span class="flex items-center">
                    <i class="fas fa-clock mr-2 text-salon-gold"></i>
                    ${variation.duration} minutes
                </span>
                <span class="text-salon-gold font-medium">Click to book</span>
            </div>
        `;

        content.appendChild(variationDiv);
    });

    document.getElementById('variationsSelectionModal').classList.remove('hidden');
}

function closeVariationsSelectionModal() {
    document.getElementById('variationsSelectionModal').classList.add('hidden');
}

function viewServiceDetails(serviceId) {
    // Show loading state
    document.getElementById('modalServiceName').textContent = 'Loading...';
    document.getElementById('modalServiceContent').innerHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-salon-gold"></div>
        </div>
    `;
    document.getElementById('serviceModal').classList.remove('hidden');

    // Fetch service details from public API
    fetch(`<?= getBasePath() ?>/api/services.php?id=${serviceId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(service => {
            document.getElementById('modalServiceName').textContent = service.name;
            document.getElementById('modalServiceContent').innerHTML = `
                <div class="space-y-4">
                    ${service.image ? `<img src="${service.image}" alt="${escapeHtml(service.name)}" class="w-full h-64 object-cover rounded-lg">` : ''}
                    <div class="flex justify-between items-center">
                        <?php if (shouldShowPricing()): ?>
                            <span class="text-3xl font-bold text-salon-gold">${formatCurrency(service.price)}</span>
                        <?php else: ?>
                            <span class="text-lg font-semibold text-salon-gold">Contact for pricing</span>
                        <?php endif; ?>
                        <span class="text-gray-300">${service.duration} minutes</span>
                    </div>
                    ${service.description ? `<p class="text-gray-300">${escapeHtml(service.description)}</p>` : '<p class="text-gray-400 italic">No description available.</p>'}
                    ${service.category ? `<div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-salon-gold/20 text-salon-gold">
                        ${escapeHtml(service.category)}
                    </div>` : ''}
                    <div class="flex gap-4 pt-4">
                        <button onclick="checkServiceVariations('${service.id}', '${escapeHtml(service.name)}', ${service.price}, ${service.duration})"
                                class="flex-1 bg-salon-gold text-black py-3 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                            Book This Service
                        </button>
                        <button onclick="closeServiceModal()"
                                class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
                            Close
                        </button>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            console.error('Error fetching service details:', error);
            document.getElementById('modalServiceName').textContent = 'Error Loading Service';
            document.getElementById('modalServiceContent').innerHTML = `
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">Failed to Load Service Details</h3>
                    <p class="text-gray-300 mb-4">We couldn't load the service information. Please try again.</p>
                    <button onclick="viewServiceDetails('${serviceId}')"
                            class="bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors mr-2">
                        Retry
                    </button>
                    <button onclick="closeServiceModal()"
                            class="bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                        Close
                    </button>
                </div>
            `;
        });
}

function closeServiceModal() {
    document.getElementById('serviceModal').classList.add('hidden');
}

function formatCurrency(amount) {
    return 'TSH ' + parseInt(amount).toLocaleString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeServiceModal();
    }
});

// Close modal on backdrop click
document.getElementById('serviceModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeServiceModal();
    }
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Initialize wishlist states when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeWishlistStates();
});

// CSRF Token
const csrfToken = '<?= $_SESSION['csrf_token'] ?? '' ?>';

// Initialize wishlist heart states
function initializeWishlistStates() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');

    wishlistBtns.forEach(btn => {
        const itemType = btn.dataset.itemType;
        const itemId = btn.dataset.itemId;

        // Check if item is in wishlist
        fetch(`<?= getBasePath() ?>/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.inWishlist) {
                    updateHeartIcon(btn, true);
                }
            })
            .catch(error => console.error('Error checking wishlist:', error));
    });
}

// Toggle wishlist item
function toggleWishlist(itemType, itemId, button) {
    // Prevent event bubbling
    event.stopPropagation();

    // Add loading state
    const icon = button.querySelector('i');
    const originalClass = icon.className;
    icon.className = 'fas fa-spinner fa-spin text-white';

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateHeartIcon(button, data.inWishlist);
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Restore original icon if there was an error
        if (icon.className.includes('fa-spinner')) {
            icon.className = originalClass;
        }
    });
}

// Update heart icon appearance
function updateHeartIcon(button, inWishlist) {
    const icon = button.querySelector('i');

    if (inWishlist) {
        icon.className = 'fas fa-heart text-base';
        button.classList.remove('border-gray-500', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
        button.classList.add('border-red-400', 'text-red-400', 'bg-red-50', 'animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    } else {
        icon.className = 'far fa-heart text-base';
        button.classList.remove('border-red-400', 'text-red-400', 'bg-red-50');
        button.classList.add('border-gray-500', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
    }
}

// Show toast notification with smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeft = '4px solid #10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else {
        toast.style.borderLeft = '4px solid #ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger smooth slide-in animation
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Lazy Loading Implementation for Services
class ServiceLazyLoader {
    constructor() {
        this.imageObserver = null;
        this.init();
    }

    init() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.observeImages();
        } else {
            // Fallback for browsers without Intersection Observer
            this.loadAllImages();
        }
    }

    observeImages() {
        const lazyImages = document.querySelectorAll('.lazy-image');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(img) {
        const placeholder = img.previousElementSibling;

        // Create a new image to preload
        const imageLoader = new Image();

        imageLoader.onload = () => {
            // Image loaded successfully
            img.src = img.dataset.src;
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');

            // Hide placeholder with fade effect
            if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                placeholder.style.transition = 'opacity 0.3s ease-out';
                placeholder.style.opacity = '0';
                setTimeout(() => {
                    placeholder.style.display = 'none';
                }, 300);
            }

            // Add loaded class for any additional styling
            img.classList.add('lazy-loaded');
        };

        imageLoader.onerror = () => {
            // Handle image load error
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');

            if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                placeholder.style.display = 'none';
            }
        };

        // Start loading the image
        imageLoader.src = img.dataset.src;
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const lazyImages = document.querySelectorAll('.lazy-image');
        lazyImages.forEach(img => {
            this.loadImage(img);
        });
    }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new ServiceLazyLoader();

    // Load subcategories if category is already selected
    const categorySelect = document.getElementById('category');
    if (categorySelect && categorySelect.value) {
        loadSubcategoriesFilter();

        // Set the selected subcategory if it exists in URL
        const urlParams = new URLSearchParams(window.location.search);
        const selectedSubcategory = urlParams.get('subcategory');
        if (selectedSubcategory) {
            setTimeout(() => {
                const subcategorySelect = document.getElementById('subcategory');
                if (subcategorySelect) {
                    subcategorySelect.value = selectedSubcategory;
                }
            }, 500); // Wait for subcategories to load
        }
    }

    // Add lazy loading styles
    const style = document.createElement('style');
    style.textContent = `
        .lazy-image {
            transition: opacity 0.3s ease-in-out;
        }

        .lazy-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .lazy-loaded {
            z-index: 2;
        }

        /* Pulse animation for loading placeholder */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    `;
    document.head.appendChild(style);
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
