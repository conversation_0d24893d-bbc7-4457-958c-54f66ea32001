<?php
/**
 * AJAX Endpoint for Backup Codes Generation
 * Separate endpoint to avoid session issues during backup codes generation
 */

// Set JSON response header
header('Content-Type: application/json');

// Start output buffering to catch any unexpected output
ob_start();

try {
    require_once __DIR__ . '/../../config/app.php';

    // Debug session information
    error_log("AJAX backup codes: Session ID: " . session_id());
    error_log("AJAX backup codes: User ID: " . ($_SESSION['user_id'] ?? 'none'));
    error_log("AJAX backup codes: User Role: " . ($_SESSION['user_role'] ?? 'none'));
    error_log("AJAX backup codes: isLoggedIn(): " . (isLoggedIn() ? 'true' : 'false'));

    // Check if user is admin
    if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
        throw new Exception('Unauthorized access - User ID: ' . ($_SESSION['user_id'] ?? 'none') . ', Role: ' . ($_SESSION['user_role'] ?? 'none') . ', Logged in: ' . (isLoggedIn() ? 'true' : 'false'));
    }
    
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    $currentUserId = $_SESSION['user_id'];
    
    // Log the start of the process
    error_log("AJAX backup codes generation started for user: $currentUserId");
    
    // Include required functions
    require_once __DIR__ . '/../../includes/functions.php';
    require_once __DIR__ . '/../../includes/admin_2fa_functions.php';
    
    // Verify functions are available
    if (!function_exists('generateBackupCodes')) {
        throw new Exception('generateBackupCodes function not available');
    }
    if (!function_exists('storeAdminBackupCodes')) {
        throw new Exception('storeAdminBackupCodes function not available');
    }
    if (!function_exists('generateUUID')) {
        throw new Exception('generateUUID function not available');
    }
    
    error_log("AJAX backup codes: All functions verified");
    
    // Generate backup codes
    $newCodes = generateBackupCodes(10);
    error_log("AJAX backup codes: Generated " . count($newCodes) . " codes");
    
    // Store backup codes
    $result = storeAdminBackupCodes($currentUserId, $newCodes);
    error_log("AJAX backup codes: Storage result - " . json_encode($result));
    
    if (!$result['success']) {
        throw new Exception($result['error']);
    }
    
    // Clean any output buffer
    ob_clean();
    
    // Return success response with codes
    echo json_encode([
        'success' => true,
        'message' => 'Backup codes generated successfully',
        'codes' => $newCodes,
        'codes_count' => count($newCodes)
    ]);
    
    error_log("AJAX backup codes: Generation completed successfully for user $currentUserId");
    
} catch (Exception $e) {
    // Clean any output buffer
    ob_clean();
    
    error_log("AJAX backup codes generation failed: " . $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// End output buffering
ob_end_flush();
?>
