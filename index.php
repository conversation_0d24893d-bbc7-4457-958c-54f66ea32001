<?php
/**
 * Main Landing Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Get featured services (randomly selected on each page load)
$featuredServices = $database->fetchAll(
    "SELECT * FROM services WHERE is_active = 1 ORDER BY RAND() LIMIT 6"
);

// Get featured packages
$featuredPackages = $database->fetchAll(
    "SELECT * FROM packages WHERE is_active = 1 ORDER BY created_at DESC LIMIT 3"
);

// Get active offers
$activeOffers = $database->fetchAll(
    "SELECT * FROM offers WHERE is_active = 1 AND valid_from <= NOW() AND valid_to >= NOW() ORDER BY created_at DESC LIMIT 3"
);

// Get gallery images
$galleryImages = $database->fetchAll(
    "SELECT * FROM gallery WHERE is_active = 1 ORDER BY created_at DESC LIMIT 8"
);

// Get recent blog posts
$blogPosts = $database->fetchAll(
    "SELECT * FROM blog_posts WHERE status = 'published' ORDER BY publish_date DESC LIMIT 3"
);

$pageTitle = "Luxury Beauty Salon";
$pageDescription = "Experience luxury and elegance at Flix Salon. Premium beauty services with a touch of sophistication.";

include __DIR__ . '/includes/header.php';
?>

<!-- Preload critical images for better performance -->
<link rel="preload" as="image" href="https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" media="(min-width: 768px)">
<link rel="preload" as="image" href="https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" media="(min-width: 768px)">

<style>
/* Lazy loading styles */
.lazy-image {
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
}

.lazy-image.loaded {
    opacity: 1;
}

.lazy-placeholder {
    background: linear-gradient(90deg, #141414 25%, #1a1a1a 50%, #141414 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Loading skeleton for images */
.image-skeleton {
    background: linear-gradient(90deg, #0a0a0a 25%, #141414 50%, #0a0a0a 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    position: relative;
}

.image-skeleton::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.3;
}

/* Fade-in animation for loaded images */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in-loaded {
    animation: fadeIn 0.5s ease-out;
}

/* Back to Top Button Styles */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: 2px solid rgba(245, 158, 11, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
    backdrop-filter: blur(10px);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4);
    border-color: rgba(245, 158, 11, 0.6);
}

.back-to-top:active {
    transform: translateY(0) scale(0.95);
}

.back-to-top svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #000000;
    transition: transform 0.2s ease;
}

.back-to-top:hover svg {
    transform: translateY(-1px);
}

/* Header Auto-Hide Styles - REMOVED to keep header always visible */

/* Responsive adjustments for back to top button */
@media (max-width: 768px) {
    .back-to-top {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 3rem;
        height: 3rem;
    }

    .back-to-top svg {
        width: 1rem;
        height: 1rem;
    }
}
</style>

<!-- Hero Carousel Section -->
<section class="relative h-screen overflow-hidden bg-black">
    <div class="hero-carousel relative h-full">
        <!-- Slide 1: Facial/Self-Care Focus -->
        <div class="hero-slide active absolute inset-0">
            <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
                 style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            </div>
            <div class="relative z-10 flex h-full items-center justify-center text-center text-white">
                <div class="max-w-4xl px-6">
                    <div class="mb-4 inline-block rounded-full bg-salon-gold/20 px-6 py-2 text-sm font-medium text-salon-gold backdrop-blur-sm">
                        ✨ Glow & Radiance
                    </div>
                    <h1 class="mb-6 text-5xl font-bold leading-tight md:text-7xl">
                        Beauty Begins Here
                    </h1>
                    <p class="mb-8 text-xl text-gray-300 max-w-2xl mx-auto">
                        Glow from the inside out with expert facials, skincare, and pampering tailored just for you.
                    </p>
                    <a href="<?= getBasePath() ?>/services?category=Facial" class="inline-flex items-center rounded-lg bg-salon-gold px-8 py-4 text-lg font-semibold text-black transition-all hover:bg-gold-light hover:scale-105">
                        Book a Glow Session
                    </a>
                </div>
            </div>
        </div>

        <!-- Slide 2: Hair Styling Focus -->
        <div class="hero-slide absolute inset-0 opacity-0">
            <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
                 style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1562322140-8baeececf3df?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            </div>
            <div class="relative z-10 flex h-full items-center justify-center text-center text-white">
                <div class="max-w-4xl px-6">
                    <div class="mb-4 inline-block rounded-full bg-salon-gold/20 px-6 py-2 text-sm font-medium text-salon-gold backdrop-blur-sm">
                        💇‍♀️ Hair Artistry
                    </div>
                    <h1 class="mb-6 text-5xl font-bold leading-tight md:text-7xl">
                        Your Hair, Your Crown
                    </h1>
                    <p class="mb-8 text-xl text-gray-300 max-w-2xl mx-auto">
                        Step into style with signature cuts, flawless blowouts, and bold color transformations.
                    </p>
                    <a href="<?= getBasePath() ?>/services?category=Hair" class="inline-flex items-center rounded-lg bg-salon-gold px-8 py-4 text-lg font-semibold text-black transition-all hover:bg-gold-light hover:scale-105">
                        Explore Hair Services
                    </a>
                </div>
            </div>
        </div>

        <!-- Slide 3: Spa & Massage Focus -->
        <div class="hero-slide absolute inset-0 opacity-0">
            <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
                 style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1544161515-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            </div>
            <div class="relative z-10 flex h-full items-center justify-center text-center text-white">
                <div class="max-w-4xl px-6">
                    <div class="mb-4 inline-block rounded-full bg-salon-gold/20 px-6 py-2 text-sm font-medium text-salon-gold backdrop-blur-sm">
                        💆 Wellness & Relaxation
                    </div>
                    <h1 class="mb-6 text-5xl font-bold leading-tight md:text-7xl">
                        Unwind. Recharge. Glow.
                    </h1>
                    <p class="mb-8 text-xl text-gray-300 max-w-2xl mx-auto">
                        Discover deep relaxation with our spa rituals, full-body massages, and detox treatments.
                    </p>
                    <a href="<?= getBasePath() ?>/services?category=Massage" class="inline-flex items-center rounded-lg bg-salon-gold px-8 py-4 text-lg font-semibold text-black transition-all hover:bg-gold-light hover:scale-105">
                        View Spa Menu
                    </a>
                </div>
            </div>
        </div>

        <!-- Slide 4: Nails Focus -->
        <div class="hero-slide absolute inset-0 opacity-0">
            <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
                 style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1604654894610-df63bc536371?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');">
            </div>
            <div class="relative z-10 flex h-full items-center justify-center text-center text-white">
                <div class="max-w-4xl px-6">
                    <div class="mb-4 inline-block rounded-full bg-salon-gold/20 px-6 py-2 text-sm font-medium text-salon-gold backdrop-blur-sm">
                        💅 Nail Artistry
                    </div>
                    <h1 class="mb-6 text-5xl font-bold leading-tight md:text-7xl">
                        Elegance at Your Fingertips
                    </h1>
                    <p class="mb-8 text-xl text-gray-300 max-w-2xl mx-auto">
                        From classic manicures to trending nail art — let your hands do the talking.
                    </p>
                    <a href="<?= getBasePath() ?>/services?category=Nails" class="inline-flex items-center rounded-lg bg-salon-gold px-8 py-4 text-lg font-semibold text-black transition-all hover:bg-gold-light hover:scale-105">
                        Book a Nail Session
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation arrows -->
    <button class="hero-prev absolute left-6 top-1/2 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 backdrop-blur-sm rounded-full p-4 transition-all duration-300 border border-white/20 hover:border-salon-gold">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
    </button>
    <button class="hero-next absolute right-6 top-1/2 -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 backdrop-blur-sm rounded-full p-4 transition-all duration-300 border border-white/20 hover:border-salon-gold">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </button>

    <!-- Dots indicator -->
    <div class="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-4">
        <button class="hero-dot active w-4 h-4 rounded-full bg-salon-gold transition-all duration-300" data-slide="0"></button>
        <button class="hero-dot w-4 h-4 rounded-full bg-white/50 transition-all duration-300" data-slide="1"></button>
        <button class="hero-dot w-4 h-4 rounded-full bg-white/50 transition-all duration-300" data-slide="2"></button>
        <button class="hero-dot w-4 h-4 rounded-full bg-white/50 transition-all duration-300" data-slide="3"></button>
    </div>
</section>

<!-- Service Categories Section -->
<section class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-white mb-4">Our Services</h2>
            <p class="text-gray-300 text-lg max-w-2xl mx-auto">
                Discover our comprehensive range of luxury beauty services designed to enhance your natural beauty
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php
            $serviceCategories = [
                ['name' => 'Hair Styling', 'icon' => '✂️', 'description' => 'Professional cuts, styling, and treatments'],
                ['name' => 'Hair Coloring', 'icon' => '🎨', 'description' => 'Expert coloring and highlighting services'],
                ['name' => 'Facial Treatments', 'icon' => '✨', 'description' => 'Rejuvenating facial treatments and skincare'],
                ['name' => 'Manicure & Pedicure', 'icon' => '💅', 'description' => 'Complete nail care and nail art'],
                ['name' => 'Massage Therapy', 'icon' => '💆', 'description' => 'Relaxing therapeutic massage services'],
                ['name' => 'Bridal Services', 'icon' => '👰', 'description' => 'Complete bridal beauty packages']
            ];

            foreach ($serviceCategories as $category): ?>
                <div class="bg-secondary-900 rounded-lg p-6 text-center hover:bg-secondary-800 transition-colors group border border-secondary-700">
                    <div class="text-4xl mb-4"><?= $category['icon'] ?></div>
                    <h3 class="text-xl font-semibold text-white mb-2"><?= $category['name'] ?></h3>
                    <p class="text-gray-300 mb-4"><?= $category['description'] ?></p>
                    <a href="<?= getBasePath() ?>/services" class="inline-flex items-center text-amber-400 hover:text-amber-300 transition-colors">
                        Learn More
                        <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Services Section -->
<section class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Popular Services
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                Featured <span class="text-salon-gold">Beauty Services</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Discover our most popular services that our clients love and trust for their beauty transformations
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($featuredServices as $index => $service): ?>
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl overflow-hidden hover:border-salon-gold/50 transition-all duration-300 hover:scale-105 hover:shadow-xl group relative">
                    <?php if ($index === 0): ?>
                        <div class="absolute top-4 left-4 bg-salon-gold text-black text-xs font-semibold px-2 py-1 rounded-full z-10">
                            Popular
                        </div>
                    <?php endif; ?>

                    <div class="relative h-48 bg-gradient-to-br from-salon-gold/20 to-secondary-900 overflow-hidden">
                        <?php if ($service['image']): ?>
                            <?php 
                            // Handle both uploaded files and external URLs
                            $imageSrc = $service['image'];
                            if (!filter_var($imageSrc, FILTER_VALIDATE_URL)) {
                                // If not a URL, treat as uploaded file and prepend uploads path
                                $imageSrc = getBasePath() . '/uploads/' . ltrim($imageSrc, '/');
                            }
                            ?>
                            <div class="image-skeleton w-full h-full absolute inset-0"></div>
                            <img src="<?= htmlspecialchars($imageSrc) ?>" alt="<?= htmlspecialchars($service['name']) ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 lazy-image" loading="lazy" decoding="async" onload="this.classList.add('loaded', 'fade-in-loaded'); this.previousElementSibling.style.display='none';" onerror="this.previousElementSibling.style.display='block';">
                        <?php else: ?>
                            <div class="w-full h-full flex items-center justify-center">
                                <div class="text-center">
                                    <span class="text-4xl text-salon-gold/60 mb-2 block">✨</span>
                                    <div class="text-gray-300 text-sm"><?= htmlspecialchars($service['category']) ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2 group-hover:text-salon-gold transition-colors duration-300">
                            <?= htmlspecialchars($service['name']) ?>
                        </h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">
                            <?= htmlspecialchars($service['description']) ?>
                        </p>

                        <div class="flex justify-between items-center mb-4">
                            <?php if (shouldShowPricing()): ?>
                                <div class="text-2xl font-bold text-salon-gold">
                                    <?= formatCurrency($service['price'], null, true) ?>
                                </div>
                            <?php else: ?>
                                <div class="text-lg font-semibold text-salon-gold">
                                    Contact for pricing
                                </div>
                            <?php endif; ?>
                            <div class="flex items-center text-gray-400 text-sm">
                                <i class="fas fa-clock mr-1"></i>
                                <?= $service['duration'] ?> min
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-1">
                                <?php for ($i = 0; $i < 5; $i++): ?>
                                    <i class="fas fa-star text-salon-gold text-xs"></i>
                                <?php endfor; ?>
                                <?php
                                // Generate dynamic rating and review count
                                $rating = number_format(rand(47, 50) / 10, 1); // 4.7 to 5.0
                                $reviewCount = rand(150, 350); // Random review count
                                ?>
                                <span class="text-xs text-gray-400 ml-2"><?= $rating ?> (<?= $reviewCount ?>)</span>
                            </div>

                            <!-- Wishlist Heart Icon -->
                            <button onclick="toggleWishlist('service', '<?= $service['id'] ?>', this)"
                                    class="wishlist-btn p-2 rounded-lg border border-gray-600 text-gray-300 hover:border-red-400 hover:text-red-400 transition-all duration-300"
                                    data-item-type="service"
                                    data-item-id="<?= $service['id'] ?>"
                                    title="Add to wishlist">
                                <i class="far fa-heart text-lg"></i>
                            </button>
                        </div>

                        <a href="<?= getBasePath() ?>/customer/book?service=<?= $service['id'] ?>" class="mt-4 w-full bg-salon-gold hover:bg-yellow-500 text-black py-3 px-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 inline-block text-center">
                            Book Now
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Service Showcase Section -->
<section class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Signature Services
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                Transform Your <span class="text-salon-gold">Beauty Journey</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Experience our signature treatments designed to enhance your natural beauty and boost your confidence
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Hair Transformation -->
            <div class="relative group">
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 hover:border-salon-gold/50 transition-all duration-300 hover:scale-105">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-cut text-salon-gold text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">Hair Transformation</h3>
                            <p class="text-salon-gold font-semibold">SIGNATURE STYLES</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        From classic cuts to modern trends, our expert stylists create looks that enhance your natural beauty and reflect your personal style.
                    </p>
                    <div class="flex items-center justify-between">
                        <a href="<?= getBasePath() ?>/services" class="text-salon-gold hover:text-yellow-400 font-semibold transition-colors">
                            Explore Styles →
                        </a>
                        <div class="flex items-center text-gray-400">
                            <i class="fas fa-users mr-2"></i>
                            <span class="font-bold text-white">2K+</span>
                            <span class="ml-1">Transformations</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bridal Beauty -->
            <div class="relative group">
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-8 hover:border-salon-gold/50 transition-all duration-300 hover:scale-105">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-crown text-salon-gold text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">Bridal Beauty</h3>
                            <p class="text-salon-gold font-semibold">YOUR PERFECT DAY</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        Complete bridal packages with trial sessions to ensure you look perfect on your special day. Every detail crafted to perfection.
                    </p>
                    <div class="flex items-center justify-between">
                        <a href="<?= getBasePath() ?>/services" class="text-salon-gold hover:text-yellow-400 font-semibold transition-colors">
                            View Packages →
                        </a>
                        <div class="flex items-center text-gray-400">
                            <i class="fas fa-star mr-2"></i>
                            <span class="font-bold text-white">5.0</span>
                            <span class="ml-1">Rating</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Feature Highlights Section -->
<section class="py-24 bg-salon-black">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Why Choose Us
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                The <span class="text-salon-gold">Flix Salon</span> Difference
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Experience the highest standards of beauty care with our commitment to excellence, luxury, and personalized service.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php
            $features = [
                [
                    'icon' => 'fas fa-shield-alt',
                    'title' => 'Premium Products',
                    'description' => 'We use only the finest, professional-grade beauty products from leading brands worldwide.'
                ],
                [
                    'icon' => 'fas fa-clock',
                    'title' => 'Flexible Scheduling',
                    'description' => 'Book appointments that fit your busy lifestyle with our convenient online booking system.'
                ],
                [
                    'icon' => 'fas fa-award',
                    'title' => 'Expert Stylists',
                    'description' => 'Our certified professionals have years of experience and stay updated with latest trends.'
                ],
                [
                    'icon' => 'fas fa-heart',
                    'title' => 'Personalized Care',
                    'description' => 'Every treatment is customized to your unique needs and beauty goals.'
                ],
                [
                    'icon' => 'fas fa-sparkles',
                    'title' => 'Luxury Experience',
                    'description' => 'Enjoy a relaxing, upscale environment designed for your comfort and satisfaction.'
                ],
                [
                    'icon' => 'fas fa-star',
                    'title' => 'Satisfaction Guaranteed',
                    'description' => 'We stand behind our work with a 100% satisfaction guarantee on all services.'
                ]
            ];

            foreach ($features as $feature): ?>
                <div class="group">
                    <div class="relative p-8 rounded-2xl bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 hover:border-salon-gold/50 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="absolute inset-0 bg-gradient-to-br from-salon-gold/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>

                        <div class="relative z-10">
                            <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mb-6 group-hover:bg-salon-gold/30 transition-colors duration-300">
                                <i class="<?= $feature['icon'] ?> text-salon-gold text-2xl"></i>
                            </div>

                            <h3 class="text-xl font-bold text-white mb-4 group-hover:text-salon-gold transition-colors duration-300">
                                <?= $feature['title'] ?>
                            </h3>

                            <p class="text-gray-300 leading-relaxed">
                                <?= $feature['description'] ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Testimonial Quote -->
        <div class="text-center mt-16">
            <div class="max-w-4xl mx-auto">
                <blockquote class="text-2xl md:text-3xl font-serif text-white mb-6 leading-relaxed">
                    "Flix Salon doesn't just provide beauty services – they create experiences that make you feel
                    <span class="text-salon-gold">confident, beautiful, and truly special</span>."
                </blockquote>
                <div class="flex items-center justify-center space-x-4">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <span class="text-salon-gold font-semibold">SC</span>
                    </div>
                    <div class="text-left">
                        <div class="text-white font-semibold">Sarah Chen</div>
                        <div class="text-gray-400 text-sm">Regular Client</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Enhanced Section -->
<section class="py-24 bg-secondary-900/50">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Client Reviews
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                What Our <span class="text-salon-gold">Clients Say</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Don't just take our word for it. Here's what our valued clients have to say about their experience at Flix Salon.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <?php
            $testimonials = [
                [
                    'name' => 'Amina Hassan',
                    'role' => 'Marketing Manager',
                    'content' => 'Absolutely amazing experience! The staff is incredibly talented and the atmosphere is so relaxing. I always leave feeling like a new person.',
                    'rating' => 5,
                    'service' => 'Hair & Makeup',
                    'image' => '/testimonials/amina.jpg'
                ],
                [
                    'name' => 'Fatima Al-Zahra',
                    'role' => 'Bride',
                    'content' => 'They made my wedding day perfect! The bridal package was everything I dreamed of and more. Professional, beautiful, and stress-free.',
                    'rating' => 5,
                    'service' => 'Bridal Package',
                    'image' => '/testimonials/fatima.jpg'
                ],
                [
                    'name' => 'Zara Khalifa',
                    'role' => 'Modal',
                    'content' => 'I love the modern yet elegant atmosphere. The stylists really listen to what you want and provide expert advice. Highly recommend!',
                    'rating' => 5,
                    'service' => 'Color & Cut',
                    'image' => '/testimonials/zara.jpg'
                ]
            ];

            foreach ($testimonials as $testimonial): ?>
                <div class="group">
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-xl p-6 hover:border-salon-gold/50 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <!-- Rating -->
                        <div class="mb-4">
                            <div class="flex space-x-1">
                                <?php for ($i = 0; $i < 5; $i++): ?>
                                    <i class="fas fa-star <?= $i < $testimonial['rating'] ? 'text-salon-gold' : 'text-gray-400' ?> text-sm"></i>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <!-- Content -->
                        <blockquote class="text-gray-300 mb-6 leading-relaxed">
                            "<?= $testimonial['content'] ?>"
                        </blockquote>

                        <!-- Client Info -->
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                <span class="text-salon-gold font-semibold text-sm">
                                    <?= strtoupper(substr($testimonial['name'], 0, 1)) . strtoupper(substr(explode(' ', $testimonial['name'])[1] ?? '', 0, 1)) ?>
                                </span>
                            </div>
                            <div>
                                <div class="text-white font-semibold"><?= $testimonial['name'] ?></div>
                                <div class="text-gray-400 text-sm"><?= $testimonial['role'] ?></div>
                                <div class="text-salon-gold text-xs"><?= $testimonial['service'] ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Overall Rating -->
        <div class="text-center mt-16">
            <div class="bg-salon-gold/5 border border-salon-gold/20 rounded-2xl p-8 max-w-3xl mx-auto">
                <div class="flex items-center justify-center space-x-8 mb-6">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-salon-gold mb-2">4.9</div>
                        <div class="flex justify-center space-x-1 mb-2">
                            <?php for ($i = 0; $i < 5; $i++): ?>
                                <i class="fas fa-star text-salon-gold"></i>
                            <?php endfor; ?>
                        </div>
                        <div class="text-gray-400 text-sm">Average Rating</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-salon-gold mb-2">500+</div>
                        <div class="text-gray-400 text-sm">Happy Clients</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-salon-gold mb-2">98%</div>
                        <div class="text-gray-400 text-sm">Satisfaction Rate</div>
                    </div>
                </div>
                <p class="text-gray-300 text-lg">
                    Join hundreds of satisfied clients who trust Flix Salon for their beauty needs.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-salon-black">
    <div class="max-w-4xl mx-auto px-6 text-center">
        <div class="bg-gradient-to-r from-salon-gold/10 to-transparent border border-salon-gold/20 rounded-3xl p-12">
            <div class="mb-8">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-4">
                    Stay <span class="text-salon-gold">Beautiful</span> with Us
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Subscribe to our newsletter for exclusive beauty tips, special offers, and the latest trends in hair and beauty.
                </p>
            </div>

            <form class="max-w-md mx-auto" action="<?= getBasePath() ?>/api/newsletter.php" method="POST">
                <div class="flex flex-col sm:flex-row gap-4">
                    <input
                        type="email"
                        name="email"
                        placeholder="Enter your email address"
                        class="flex-1 px-6 py-4 bg-secondary-800/50 border border-secondary-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent"
                        required
                    >
                    <button
                        type="submit"
                        class="px-8 py-4 bg-salon-gold hover:bg-yellow-500 text-black font-semibold rounded-xl transition-all duration-300 hover:scale-105 whitespace-nowrap"
                    >
                        Subscribe Now
                    </button>
                </div>
                <p class="text-gray-400 text-sm mt-4">
                    No spam, unsubscribe at any time. We respect your privacy.
                </p>
            </form>

            <div class="flex items-center justify-center space-x-8 mt-8 pt-8 border-t border-salon-gold/20">
                <div class="text-center">
                    <div class="text-2xl font-bold text-salon-gold">5K+</div>
                    <div class="text-gray-400 text-sm">Subscribers</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-salon-gold">Weekly</div>
                    <div class="text-gray-400 text-sm">Beauty Tips</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-salon-gold">Exclusive</div>
                    <div class="text-gray-400 text-sm">Offers</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Section -->
<section class="py-24 bg-salon-black">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Beauty Blog
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                Latest <span class="text-salon-gold">Beauty Insights</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Stay updated with the latest beauty trends, tips, and expert advice from our professional stylists.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($blogPosts as $post): 
                // Calculate estimated read time based on content length
                $wordCount = str_word_count(strip_tags($post['full_content']));
                $readTime = max(1, ceil($wordCount / 200)) . ' min read'; // Average reading speed 200 words/min
                
                // Use summary if available, otherwise truncate full content
                $excerpt = !empty($post['summary']) ? $post['summary'] : substr(strip_tags($post['full_content']), 0, 150) . '...';
                
                // Format the publish date
                $publishDate = $post['publish_date'] ? date('Y-m-d', strtotime($post['publish_date'])) : date('Y-m-d', strtotime($post['created_at']));
            ?>
                <article class="group">
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-xl overflow-hidden hover:border-salon-gold/50 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                        <div class="relative h-48 bg-gradient-to-br from-salon-gold/20 to-secondary-900 overflow-hidden">
                            <?php if (!empty($post['image_url'])): ?>
                                <div class="image-skeleton w-full h-full absolute inset-0"></div>
                                <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>" alt="<?= htmlspecialchars($post['title']) ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 lazy-image" loading="lazy" decoding="async" onload="this.classList.add('loaded', 'fade-in-loaded'); this.previousElementSibling.style.display='none';" onerror="this.previousElementSibling.style.display='block';">
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center">
                                    <div class="text-center">
                                        <i class="fas fa-newspaper text-4xl text-salon-gold/60 mb-2"></i>
                                        <div class="text-gray-300 text-sm">Blog Post</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="absolute top-4 left-4">
                                <span class="bg-salon-gold text-black text-xs font-semibold px-2 py-1 rounded-full">
                                    Blog Post
                                </span>
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="flex items-center text-gray-400 text-sm mb-3">
                                <i class="fas fa-calendar mr-2"></i>
                                <?= date('M j, Y', strtotime($publishDate)) ?>
                                <span class="mx-2">•</span>
                                <i class="fas fa-clock mr-2"></i>
                                <?= $readTime ?>
                            </div>

                            <h3 class="text-xl font-bold text-white mb-3 group-hover:text-salon-gold transition-colors duration-300 leading-tight">
                                <?= htmlspecialchars($post['title']) ?>
                            </h3>

                            <p class="text-gray-300 mb-4 leading-relaxed">
                                <?= htmlspecialchars($excerpt) ?>
                            </p>

                            <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>" class="inline-flex items-center text-salon-gold hover:text-yellow-400 font-semibold transition-colors duration-300">
                                Read More
                                <i class="fas fa-arrow-right ml-2 text-sm"></i>
                            </a>
                        </div>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-12">
            <a href="blog.php" class="inline-flex items-center bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                View All Articles
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Instagram Feed Section -->
<section class="py-24 bg-salon-black">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-4">
                Follow Us
            </div>
            <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                <span class="text-salon-gold">@flix.tz</span> on Instagram
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
                Get daily inspiration, behind-the-scenes content, and see the latest transformations from our talented team.
            </p>
        </div>

        <!-- Instagram Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-12">
            <?php
            // Real Instagram-style posts with actual images from Unsplash
            $instagramPosts = [
                [
                    'id' => 1,
                    'image' => 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                    'likes' => rand(180, 320),
                    'comments' => rand(12, 28),
                    'caption' => 'Stunning balayage transformation ✨ Book your appointment today! #FlixTZ #HairGoals #BalayageExpert'
                ],
                [
                    'id' => 2,
                    'image' => 'https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                    'likes' => rand(150, 280),
                    'comments' => rand(8, 22),
                    'caption' => 'Bridal perfection for Amina\'s special day 👰✨ Every bride deserves to feel like a queen #BridalBeauty #FlixTZ'
                ],
                [
                    'id' => 3,
                    'image' => 'https://images.unsplash.com/photo-1562322140-8baeececf3df?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                    'likes' => rand(200, 350),
                    'comments' => rand(15, 35),
                    'caption' => 'Fresh cut and style for the new season 💇‍♀️ Who\'s ready for a transformation? #NewLook #FlixTZ #HairCut'
                ],
                [
                    'id' => 4,
                    'image' => 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                    'likes' => rand(220, 380),
                    'comments' => rand(18, 42),
                    'caption' => 'Glowing skin after our signature facial treatment 💆‍♀️✨ Self-care Sunday vibes #SkinCare #FlixTZ #GlowUp'
                ],
                [
                    'id' => 5,
                    'image' => 'https://images.unsplash.com/photo-1516975080664-ed2fc6a32937?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                    'likes' => rand(160, 290),
                    'comments' => rand(10, 25),
                    'caption' => 'Color transformation magic happening right here ✨🎨 From brunette to blonde goddess #HairColor #Transformation #FlixTZ'
                ],
                [
                    'id' => 6,
                    'image' => 'https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                    'likes' => rand(190, 310),
                    'comments' => rand(12, 30),
                    'caption' => 'Weekend vibes with our relaxing spa treatments 🧖‍♀️💅 Treat yourself, you deserve it! #SpaDay #FlixTZ #Relaxation'
                ]
            ];

            foreach ($instagramPosts as $index => $post): ?>
                <div class="group relative aspect-square overflow-hidden rounded-lg bg-secondary-900 hover:scale-105 transition-transform duration-300">
                    <!-- Real Instagram Post Image -->
                    <div class="image-skeleton w-full h-full absolute inset-0"></div>
                    <img src="<?= $post['image'] ?>" alt="Instagram post" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 lazy-image" loading="lazy" decoding="async" onload="this.classList.add('loaded', 'fade-in-loaded'); this.previousElementSibling.style.display='none';" onerror="this.previousElementSibling.style.display='block';">

                    <!-- Instagram-style gradient overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>

                    <!-- Hover Overlay with Instagram-style stats -->
                    <div class="absolute inset-0 bg-black/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <div class="text-center text-white">
                            <div class="flex items-center justify-center space-x-6 mb-3">
                                <div class="flex items-center">
                                    <i class="fas fa-heart text-red-400 mr-2 text-lg"></i>
                                    <span class="text-sm font-semibold"><?= number_format($post['likes']) ?></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-comment text-blue-400 mr-2 text-lg"></i>
                                    <span class="text-sm font-semibold"><?= $post['comments'] ?></span>
                                </div>
                            </div>
                            <p class="text-xs px-3 leading-tight text-gray-200"><?= substr($post['caption'], 0, 60) ?>...</p>
                        </div>
                    </div>

                    <!-- Instagram-style corner icon -->
                    <div class="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <i class="fab fa-instagram text-white text-lg"></i>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="bg-salon-gold/5 border border-salon-gold/20 rounded-2xl p-8 max-w-3xl mx-auto mb-8">
                <i class="fas fa-camera text-salon-gold text-4xl mb-6"></i>
                <h3 class="text-2xl font-bold text-white mb-4">
                    Share Your Flix Salon & SPA Experience
                </h3>
                <p class="text-gray-300 mb-6">
                    Tag us in your posts and stories for a chance to be featured on our page!
                    Use #FlixSalonSPA to share your beautiful transformations with our community.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="https://www.instagram.com/flix.tz/" target="_blank" class="inline-flex items-center bg-salon-gold hover:bg-yellow-500 text-black px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105">
                        <i class="fab fa-instagram mr-2"></i>
                        Follow @flix.tz
                    </a>
                    <a href="#" class="inline-flex items-center bg-secondary-800 hover:bg-secondary-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 border border-secondary-600">
                        <i class="fas fa-hashtag mr-2"></i>
                        Use #FlixSalonSPA
                    </a>
                </div>
            </div>

            <!-- Hashtag Section -->
            <div class="flex flex-wrap justify-center gap-3">
                <?php
                $hashtags = ['#FlixSalonSPA', '#HairGoals', '#BridalBeauty', '#SalonLife', '#BeautyTransformation', '#LuxurySalon'];
                foreach ($hashtags as $hashtag): ?>
                    <span class="bg-secondary-900/80 text-salon-gold px-3 py-1 rounded-full text-sm font-medium border border-salon-gold/20 hover:bg-salon-gold/10 transition-colors cursor-pointer">
                        <?= $hashtag ?>
                    </span>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<script>
// Enhanced Lazy Loading with Intersection Observer
function initializeLazyLoading() {
    // Check if Intersection Observer is supported
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;

                    // Add loading state
                    img.style.filter = 'blur(5px)';

                    // Create a new image to preload
                    const imageLoader = new Image();
                    imageLoader.onload = function() {
                        // Image loaded successfully
                        img.style.filter = 'none';
                        img.classList.add('loaded', 'fade-in-loaded');

                        // Hide skeleton
                        const skeleton = img.previousElementSibling;
                        if (skeleton && skeleton.classList.contains('image-skeleton')) {
                            skeleton.style.opacity = '0';
                            setTimeout(() => skeleton.style.display = 'none', 300);
                        }
                    };

                    imageLoader.onerror = function() {
                        // Image failed to load
                        const skeleton = img.previousElementSibling;
                        if (skeleton && skeleton.classList.contains('image-skeleton')) {
                            skeleton.style.display = 'block';
                        }
                        img.style.display = 'none';
                    };

                    // Start loading the image
                    imageLoader.src = img.src;

                    // Stop observing this image
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all lazy images
        document.querySelectorAll('.lazy-image').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Preload critical images (above the fold)
function preloadCriticalImages() {
    const criticalImages = document.querySelectorAll('.lazy-image[loading="eager"]');
    criticalImages.forEach(img => {
        if (img.complete) {
            img.classList.add('loaded');
        } else {
            img.addEventListener('load', () => {
                img.classList.add('loaded', 'fade-in-loaded');
            });
        }
    });
}

// Hero carousel functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize lazy loading
    initializeLazyLoading();
    preloadCriticalImages();
    const slides = document.querySelectorAll('.hero-slide');
    const dots = document.querySelectorAll('.hero-dot');
    const prevBtn = document.querySelector('.hero-prev');
    const nextBtn = document.querySelector('.hero-next');
    let currentSlide = 0;
    const totalSlides = slides.length;

    function showSlide(index) {
        slides.forEach((slide, i) => {
            slide.style.opacity = i === index ? '1' : '0';
        });

        dots.forEach((dot, i) => {
            dot.classList.toggle('active', i === index);
            dot.classList.toggle('bg-salon-gold', i === index);
            dot.classList.toggle('bg-white/50', i !== index);
        });

        currentSlide = index;
    }

    function nextSlide() {
        showSlide((currentSlide + 1) % totalSlides);
    }

    function prevSlide() {
        showSlide((currentSlide - 1 + totalSlides) % totalSlides);
    }

    // Auto-advance slides
    setInterval(nextSlide, 5000);

    // Event listeners
    nextBtn.addEventListener('click', nextSlide);
    prevBtn.addEventListener('click', prevSlide);

    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => showSlide(index));
    });

    // Initialize wishlist states
    initializeWishlistStates();
});

// CSRF Token
const csrfToken = '<?= $_SESSION['csrf_token'] ?? '' ?>';

// Initialize wishlist heart states
function initializeWishlistStates() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');

    wishlistBtns.forEach(btn => {
        const itemType = btn.dataset.itemType;
        const itemId = btn.dataset.itemId;

        // Check if item is in wishlist
        fetch(`<?= getBasePath() ?>/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.inWishlist) {
                    updateHeartIcon(btn, true);
                }
            })
            .catch(error => console.error('Error checking wishlist:', error));
    });
}

// Toggle wishlist item
function toggleWishlist(itemType, itemId, button) {
    // Prevent event bubbling
    event.stopPropagation();

    // Add loading state
    const icon = button.querySelector('i');
    const originalClass = icon.className;
    icon.className = 'fas fa-spinner fa-spin text-white';

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateHeartIcon(button, data.inWishlist);
            showToast(data.message, 'success');
            updateWishlistBadge(data.count);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Restore original icon if there was an error
        if (icon.className.includes('fa-spinner')) {
            icon.className = originalClass;
        }
    });
}

// Update heart icon appearance
function updateHeartIcon(button, inWishlist) {
    const icon = button.querySelector('i');

    if (inWishlist) {
        icon.className = 'fas fa-heart text-lg';
        button.classList.remove('border-gray-600', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
        button.classList.add('border-red-400', 'text-red-400', 'bg-red-50', 'animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    } else {
        icon.className = 'far fa-heart text-lg';
        button.classList.remove('border-red-400', 'text-red-400', 'bg-red-50');
        button.classList.add('border-gray-600', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
    }
}

// Update wishlist badge count
function updateWishlistBadge(count) {
    const badge = document.getElementById('wishlist-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'inline-block';
        } else {
            badge.style.display = 'none';
        }
    }
}

// Show toast notification with smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeft = '4px solid #10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else {
        toast.style.borderLeft = '4px solid #ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger smooth slide-in animation
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Back to Top Button Functionality
function initializeBackToTop() {
    // Create back to top button
    const backToTopButton = document.createElement('button');
    backToTopButton.className = 'back-to-top';
    backToTopButton.setAttribute('aria-label', 'Back to top');
    backToTopButton.innerHTML = `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    `;

    // Add to DOM
    document.body.appendChild(backToTopButton);

    // Scroll to top functionality
    backToTopButton.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Show/hide button based on scroll position
    let isVisible = false;
    const toggleVisibility = () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldShow = scrollTop > 300;

        if (shouldShow && !isVisible) {
            backToTopButton.classList.add('show');
            isVisible = true;
        } else if (!shouldShow && isVisible) {
            backToTopButton.classList.remove('show');
            isVisible = false;
        }
    };

    // Throttled scroll event listener for better performance
    let ticking = false;
    const handleScroll = () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleVisibility();
                ticking = false;
            });
            ticking = true;
        }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Initial check
    toggleVisibility();
}

// Header Auto-Hide/Show Functionality - REMOVED to keep header always visible

// Ensure header stays fixed and visible
function ensureHeaderFixed() {
    const header = document.querySelector('header');
    if (header) {
        // Remove any transform styles that might hide the header
        header.style.transform = 'none';
        header.style.transition = 'none';
        // Ensure header classes are correct
        header.classList.remove('header-hidden', 'header-visible');
    }
}

// Initialize features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeBackToTop();
    ensureHeaderFixed();
    // Header auto-hide functionality removed - header will remain always visible
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
