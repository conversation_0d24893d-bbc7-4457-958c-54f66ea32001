<?php
require_once __DIR__ . '/../../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

// Check if offer ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Offer ID is required']);
    exit();
}

try {
    $offerId = $_GET['id'];
    $offer = getOfferById($offerId);
    
    if (!$offer) {
        http_response_code(404);
        echo json_encode(['error' => 'Offer not found']);
        exit();
    }
    
    // Return offer data
    header('Content-Type: application/json');
    echo json_encode($offer);
    
} catch (Exception $e) {
    error_log("Get offer API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
